import { addBusinessDays, format } from "date-fns";

import { Submission } from "../../../src/types";
import { claim, fineos, fineosPages, portal } from "../../actions";
import { ClaimPage } from "../../actions/fineos.pages";
import { unstashMultipleKeys } from "../../util";

describe("Surface key information for base claims", () => {
  context("When the employer review is completed", () => {
    it("The claimant begins the application and completes the first portion", () => {
      portal.before();
      claim.generateClaim("WDCLAIM").then(async (claim) => {
        cy.stash("claim", claim);
        const { employer_fein } = claim.claim;

        assert(employer_fein);

        portal.loginClaimant();
        portal.goToDashboardFromApplicationsPage();

        // Submit Claim
        portal.startClaimAndSubmitClaimPartOne(claim.claim, {}, claim.employer);
        portal.waitForClaimSubmission();
      });
    });

    it("Leave admin reviews the started application status", () => {
      cy.dependsOnPreviousPass();
      unstashMultipleKeys<{ submission: Submission; claim: DehydratedClaim }>([
        "submission",
        "claim",
      ]).then(({ submission, claim }) => {
        const today = Date.now();
        portal.before();
        portal.loginLeaveAdmin(claim.employer.fein);
        portal.goToEmployerApplicationsPage();
        portal.searchClaims(submission.fineos_absence_id);

        cy.contains(`Application started ${format(today, "M/d/yyyy")}`);
        cy.contains("No action required");
      });
    });

    it("The claimant submits parts two and three", () => {
      cy.dependsOnPreviousPass();
      unstashMultipleKeys<{ submission: Submission; claim: DehydratedClaim }>([
        "submission",
        "claim",
      ]).then(({ submission, claim }) => {
        portal.before();
        portal.loginClaimant();
        portal.viewClaim(submission.application_id);
        portal.submitClaimPartsTwoThree(claim.claim, claim.paymentPreference, {
          is_withholding_tax: claim.is_withholding_tax,
          withCertificationDocument: true,
        });
      });
    });

    it("Leave admin reviews the completed application", () => {
      cy.dependsOnPreviousPass();
      unstashMultipleKeys<{ submission: Submission; claim: DehydratedClaim }>([
        "submission",
        "claim",
      ]).then(({ submission, claim }) => {
        portal.before();
        portal.loginLeaveAdmin(claim.employer.fein);
        portal.goToEmployerApplicationsPage();
        portal.searchClaims(submission.fineos_absence_id);
        cy.contains("Application completed");
        cy.contains("Employer review due");
        cy.get('[data-label="To do"]').contains("Review Application");
      });
    });

    it("The employer submits their review", () => {
      cy.dependsOnPreviousPass();
      unstashMultipleKeys<{ submission: Submission; claim: DehydratedClaim }>([
        "submission",
        "claim",
      ]).then(({ submission, claim }) => {
        cy.task("submitEmployerResponseToApi", {
          employerResponse: {
            employer_benefits: [],
            employer_decision: "Approve",
            fraud: "No",
            hours_worked_per_week: {
              hours_worked: 40,
              employer_changes: "Unchanged",
            },
            previous_leaves: [],
          },
          identifiers: {
            employerFein: claim.employer.fein,
            fineosAbsenceId: submission.fineos_absence_id,
          },
        });
      });
    });

    it("Leave admin reviews the completed application after employer review", () => {
      cy.dependsOnPreviousPass();

      unstashMultipleKeys<{ submission: Submission; claim: DehydratedClaim }>([
        "submission",
        "claim",
      ]).then(({ submission, claim }) => {
        const today = Date.now();
        const dfmlDecisionExpectedDate = addBusinessDays(Date.now(), 10);
        portal.before();
        portal.loginLeaveAdmin(claim.employer.fein);
        portal.getLeaveAdminName().then((leaveAdminName) => {
          const [firstName, lastName] = leaveAdminName.split(" ");
          portal.goToEmployerApplicationsPage();
          portal.searchClaims(submission.fineos_absence_id);
          cy.contains(`Application completed ${format(today, "M/d/yyyy")}`);
          cy.get('[title="employers employer review"] > p')
            .should("contain.text", `Employer review`)
            .should("contain.text", "completed by")
            .should("contain.text", firstName)
            .should("contain.text", lastName)
            .should("contain.text", format(Date.now(), "M/d/yyyy"));
          cy.contains(
            `DFML decision expected ${format(
              dfmlDecisionExpectedDate,
              "M/d/yyyy"
            )}`
          );
          cy.get('[data-label="To do"]').contains("No action required");
          cy.get('[data-label="To do"]').contains("Employer review completed");
        });
      });
    });

    it("The DFML agent adjudicates the claim", () => {
      cy.dependsOnPreviousPass();
      unstashMultipleKeys<{ submission: Submission; claim: DehydratedClaim }>([
        "submission",
        "claim",
      ]).then(({ submission, claim }) => {
        fineos.before();
        const claimPage = ClaimPage.visit(submission.fineos_absence_id);

        claimPage
          .completeAdjudication(claim.documents, { acceptLeavePlan: true })
          .approve("Approved");

        claimPage.triggerNotice("Preliminary Designation");
      });
    });

    it("The employer reviews the application status", () => {
      cy.dependsOnPreviousPass();

      unstashMultipleKeys<{ submission: Submission; claim: DehydratedClaim }>([
        "submission",
        "claim",
      ]).then(({ submission, claim }) => {
        portal.before();
        cy.task("syncLeaveDetails", submission.fineos_absence_id).then((_) => {
          portal.loginLeaveAdmin(claim.employer.fein);
          portal.getLeaveAdminName().then((leaveAdminName) => {
            const today = Date.now();
            portal.goToEmployerApplicationsPage();
            portal.searchClaims(submission.fineos_absence_id);
            cy.contains("DFML decision reached");
            cy.contains(`Application completed ${format(today, "M/d/yyyy")}`);
            cy.get('[title="employers employer review"] > p')
              .should("contains.text", "Employer review")
              .should("contains.text", `completed by ${leaveAdminName}`)
              .should("contains.text", format(today, "M/d/yyyy"));
            cy.get('[data-label="To do"]').contains("No action required");
          });
        });
      });
    });
  });
});

describe("When the employer review is missed", () => {
  it("The claimant begins the application and completes the first portion", () => {
    portal.generateAndSubmitClaimToAPI({ scenario: "WDCLAIM" });
  });

  it("The employer misses the deadline to review claim", () => {
    cy.dependsOnPreviousPass();
    cy.unstash<Submission>("submission").then((submission) => {
      const claimPage = fineosPages.ClaimPage.visit(
        submission.fineos_absence_id
      );

      claimPage.outstandingRequirements((outstanding_requirement) => {
        outstanding_requirement.suppress(
          "Auto-Suppressed",
          "Suppress Employer Confirmation",
          true
        );
      });
    });
  });

  it("Force status update", () => {
    cy.unstash<Submission>("submission").then((submission) => {
      cy.task("syncLeaveDetails", submission.fineos_absence_id);
    });
  });

  it("The employer reviews the application status", { retries: 3 }, () => {
    cy.dependsOnPreviousPass();
    unstashMultipleKeys<{ submission: Submission; claim: DehydratedClaim }>([
      "submission",
      "claim",
    ]).then(({ submission, claim }) => {
      const today = Date.now();
      const employerReviewExpectedDate = addBusinessDays(Date.now(), 11);

      portal.before();

      portal.loginLeaveAdmin(claim.employer.fein);
      portal.goToEmployerApplicationsPage();
      portal.searchClaims(submission.fineos_absence_id);
      cy.contains("DFML decision expected");
      cy.contains(`Application completed ${format(today, "M/d/yyyy")}`);
      cy.get(`[title="employers employer review"] > p`)
        .should("contains.text", "Employer review due")
        .should(
          "contains.text",
          format(employerReviewExpectedDate, "M/d/yyyy")
        );
      cy.get('[data-label="To do"]').contains("No action required");
      cy.get('[data-label="To do"]').contains(
        "Employer review deadline missed"
      );
    });
  });
});

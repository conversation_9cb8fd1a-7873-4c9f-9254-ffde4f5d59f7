import { addMinutes, format, subMinutes } from "date-fns";

import config from "../../../../src/config";
import { claim, email, portal } from "../../../actions";
import { findSentEmailQuery } from "../../../actions/portal";

type EmailSentInfo = {
  created_at: string;
  fineos_absence_id: string;
};

// The LinkClaimToApplication job is reliant on the payment batches, which don't run in the training environments.
const environmentIsExcluded = ["trn2", "training"].includes(
  config("ENVIRONMENT")
);

describe(
  "As a claimant, I can resume my portal application after manual review",
  {},
  () => {
    beforeEach(function () {
      if (environmentIsExcluded) {
        this.skip();
      }
    });

    before(() => {
      if (environmentIsExcluded) {
        return;
      }
      portal.before();
      // find some applications successfully linked yesterday
      // If today is Monday, look back to Friday instead of "Yesterday"

      const dayOfWeek = format(new Date(), "EEEE");
      const isMonday = dayOfWeek === "Monday";
      cy.task("findApplicationsLinkedNrLogs", {
        days: isMonday ? 4 : 2,
      }).then((nrApplications) => {
        if (nrApplications.length === 0) {
          throw new Error("No applications were successfully linked yesterday");
        }
        cy.stash("nrApplications", nrApplications);
      });
    });

    const claimsNotClosedOrDeniedAfterLinking: Set<string> = new Set();
    it("Should complete an application that was manually reviewed", () => {
      cy.unstash<NrqlUNFLog[]>("nrApplications").then((nrApplications) => {
        portal.loginClaimant();
        portal.skipLoadingClaimantApplications();

        let submitted_part_3_count = 0;
        // Check within nrApplications,
        // whether the most recent applications have been linked
        // and attempt to upload documents
        // and complete them (Part 3)
        // or verify that you can see claim status
        for (const nrApplication of nrApplications) {
          // Stop after completing one application
          if (submitted_part_3_count > 0) {
            break;
          }
          // Get up-to-date info about this particular portal application
          cy.task("getApplicationById", {
            application_id: nrApplication.application_id,
          }).then((apiApplication) => {
            // Skip this application if it belongs to a different portal user
            if (
              apiApplication === null ||
              apiApplication.updated_at === undefined
            ) {
              return;
            }
            if (
              new Date(apiApplication.updated_at) >
              new Date(nrApplication.updated_at)
            ) {
              cy.log(`NR Application updated at: ${nrApplication.updated_at}`);
              cy.log(
                `API Application updated at: ${apiApplication.updated_at}`
              );
              cy.log(
                `Skipping application: ${nrApplication.fineos_absence_id}`
              );
              // Ignore this application
              return;
            }

            // if the status is not one of the two expected
            if (
              !["Submitted", "Completed"].includes(
                nrApplication.status as string
              )
            ) {
              throw new Error(
                `Unexpected application status "${nrApplication.status}": ${nrApplication.application_id}`
              );
            }

            // Attempt part 3 submission
            if (nrApplication.status === "Submitted") {
              portal.viewClaim(nrApplication.application_id);
              claim.generateClaim("UNF").then((claim) => {
                portal.submitClaimPartThree(claim.claim, {
                  is_withholding_tax: claim.is_withholding_tax,
                  withCertificationDocument: true,
                });
              });
              submitted_part_3_count += 1;
              claimsNotClosedOrDeniedAfterLinking.add(
                nrApplication.fineos_absence_id
              );
            }
            // Attempt to view completed application status
            portal.viewClaimStatus(nrApplication.fineos_absence_id);
            cy.contains(nrApplication.fineos_absence_id);
          });
          // Wait for page load before starting next application
          cy.wait(2000);
        }
      });
    });

    it(
      "Should find notifications for claims not closed or denied after linking",
      {
        retries: 0,
      },
      () => {
        cy.unstash<NrqlUNFLog[]>("nrApplications").then((nrApplications) => {
          for (const { claim_id } of nrApplications) {
            if (claimsNotClosedOrDeniedAfterLinking.has(claim_id)) {
              cy.log(
                `${claim_id} has been closed or denied, and may not have had an email sent.`
              );
              continue;
            }
            const trigger = "User Not Found Claim Linked";
            cy.task<EmailSentInfo>(
              "queryDb",
              findSentEmailQuery(claim_id, trigger)
            ).then(({ created_at, fineos_absence_id }) => {
              const approximateSendTime = new Date(created_at);
              email.getEmails({
                address: "<EMAIL>",
                subject: email.getNotificationSubject(
                  "user not found claim linked"
                ),
                message: fineos_absence_id,
                timestamp_from: subMinutes(approximateSendTime, 1).getTime(),
                timestamp_to: addMinutes(approximateSendTime, 3).getTime(),
                debugInfo: {
                  fineosClaimID: fineos_absence_id,
                  triggerType: trigger,
                  recipient: "Claimant",
                },
              });
            });
            break;
          }
        });
      }
    );
  }
);

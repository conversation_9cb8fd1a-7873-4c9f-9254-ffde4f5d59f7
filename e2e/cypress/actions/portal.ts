import {
  addDays,
  differenceInBusinessDays,
  format,
  getMonth,
  getYear,
  parseISO,
} from "date-fns";
import path from "path";
import PdfParse from "pdf-parse";

import {
  formatDateAsIsoExt,
  formatDateAsText,
  formatIsoDateStringAsText,
} from "../../global.common";
import {
  ApplicationRequestBody,
  industrySector,
  IntermittentLeavePeriods,
  Language,
  PaymentPreference,
  PaymentPreferenceRequestBody,
  ReducedScheduleLeavePeriods,
  WorkPattern,
  WorkPatternDay,
} from "../../src/api";
import config from "../../src/config";
import {
  extractDebugInfoFromBody,
  extractDebugInfoFromHeaders,
} from "../../src/errors";
import { Employer } from "../../src/generation/Employer";
import {
  BaseClaimSpecification,
  DehydratedClaim,
  LeaveReason,
} from "../../src/generation/types";
import portalFeatureFlags, {
  PortalFeatureFlagConfiguration,
} from "../../src/portalFeatureFlags";
import {
  ApplicationLeaveDetailsReason,
  ClaimGenData,
  CompletedPaymentPeriod,
  ContinuousLeavePeriod,
  Credentials,
  ValidClaim,
  ValidEmployerBenefit,
  ValidOtherIncome,
  ValidPreviousLeave,
} from "../../src/types";
import {
  calculateNextBenefitYear,
  maxAvailableLeaveTimeInWeeks,
} from "../../src/util/benefit";
import {
  dateToReviewFormat,
  getLeavePeriod,
  getReportableBenefitPeriod,
  getWaitingPeriod,
  minutesToHoursAndMinutes,
} from "../../src/util/claims";
import { getClaimantCredentials } from "../../src/util/credentials";
import {
  assertIsTypedArray,
  assertValidClaim,
  isTypedArray,
  isValidEmployerBenefit,
  isValidOtherIncome,
  isValidPreviousLeave,
} from "../../src/util/typeUtils";
import { getLeaveAdminCredentials } from "../config";
import { getDocPath, getFormPath, numToPaymentFormat } from "../util";
import { email } from ".";
import { generateClaim, generateClaimForEmployeeWithoutClaims } from "./claim";
import { inFieldsetLabelled } from "./common";

const hasProfileIdvFeature = config("HAS_PROFILE_IDV_FEATURE");
/**Set portal feature flags */
function setFeatureFlags(flags?: Partial<PortalFeatureFlagConfiguration>) {
  // Set the feature flag necessary to see the portal.
  const cookies = JSON.stringify({ ...portalFeatureFlags, ...flags });
  cy.setCookie("_ff", cookies, {
    log: true,
  });
  cy.getCookie("_ff").should("have.property", "value", cookies);
}

/**
 * Performs setup and mocking needed for running tests in the PFML Portal.
 */
export function before() {
  const cfg = Cypress.config();
  cfg.baseUrl = config("PORTAL_BASEURL");
  Cypress.config("pageLoadTimeout", 30_000);

  // Setup a route for application submission so we can extract claim ID later.
  cy.intercept({
    method: "POST",
    url: "**/api/v1/applications/*/submit+(-|_)application",
  }).as("submitClaimResponse");

  cy.intercept({
    url: /\/api\/v1\/applications\/.*\/documents/,
    method: "POST",
  }).as("documentUpload");

  cy.intercept({
    url: /\/api\/v1\/applications\/.*\/documents$/,
    method: "GET",
  }).as("getDocuments");

  cy.intercept(/\/api\/v1\/claims\?page_offset=\d+$/).as(
    "dashboardDefaultQuery"
  );
  cy.intercept(/\/api\/v1\/(applications\?|applications$)/).as(
    "getApplications"
  );
  cy.intercept(/\/benefit-years\/search/).as("benefitYearsSearch");
  cy.intercept("**/api/v1/users/current").as("currentUser");
  cy.intercept("**/api/v1/employers/claims/*/review").as("review");
  cy.intercept("**/api/v1/employers/claims/*/documents").as("documentsReview");
  cy.intercept(/\/api\/v1\/claims\?(page_offset=\d+)?&?(order_by)/).as(
    "dashboardClaimQueries"
  );
  cy.intercept({
    method: "GET",
    url: "**/api/v1/payments?absence_case_id=*",
  }).as("payments");
  cy.intercept({
    url: /\/api\/v1\/change-request*/,
    method: "GET",
  }).as("getModification");
  cy.intercept({
    url: "**/applications/status/**",
    method: "GET",
  }).as("getModificationStatus");

  cy.intercept({
    method: "PATCH",
    url: "**/api/v1/change-request/*",
  }).as("changeRequest");
  cy.intercept({
    method: "POST",
    url: "**/leave-admins/search",
  }).as("getLeaveAdmins");
  cy.intercept({
    method: "POST",
    url: "**/leave-admins/add",
  }).as("addLeaveAdmin");
  cy.intercept({
    method: "POST",
    url: "**/leave-admins/*/deactivate",
  }).as("removeLeaveAdmin");
  deleteDownloadsFolder();

  // Stub calls to Google Analytics.
  cy.intercept(
    {
      method: "POST",
      url: config("GOOGLE_ANALYTICS_PATTERN"),
    },
    (req) => {
      req.reply({ statusCode: 200 });
    }
  ).as("gAnalytics");

  cy.intercept(
    {
      method: "GET",
      url: "**/address/search*",
    },
    {
      data: {
        // Experian's fuzzy matching will often find similar real addresses,
        // so we just stub an empty response to keep application behavior
        // consistent.
        addresses: [],
      },
    }
  ).as("addressSearch");

  // @TODO: `setFeatureFlags()` and `cy.visit()` can be removed after
  //        both claimants and leave admins log in with MMG.
  setFeatureFlags();
  cy.visit("/");
}

function stubRequest(url: string | RegExp, alias: string) {
  cy.intercept(
    {
      url,
      method: "GET",
    },
    (req) => {
      req.reply((res) => {
        res.body.data = [];
        res.send();
      });
    }
  ).as(alias);
}

function unstubRequest(url: string | RegExp, alias: string) {
  cy.intercept(
    {
      url,
      method: "GET",
    },
    (req) => {
      req.continue();
    }
  ).as(alias);
}

function actionWithSkippedGetRequests(
  skips: Array<"documents" | "modifications">,
  cb: () => void
) {
  const skipOptionsMap = {
    documents: {
      url: /\/api\/v1\/applications\/.*\/documents$/,
      alias: "getDocuments",
    },
    modifications: {
      url: /\/api\/v1\/change-request*/,
      alias: "getModification",
    },
  } as const;
  for (const item of skips) {
    const { url, alias } = skipOptionsMap[item];
    stubRequest(url, alias);
  }
  cb();
  for (const item of skips) {
    const { url, alias } = skipOptionsMap[item];
    unstubRequest(url, alias);
  }
}

export function onPage(page: string): void {
  cy.url().should("include", `/applications/${page}`);
}

export interface ClaimSubmissionOptions extends ClaimGenData {
  credentials?: Credentials;
  modifyClaim?: (claim: DehydratedClaim) => Cypress.Chainable<DehydratedClaim>;
  findCleanEmployee?: boolean;
}

export function generateAndSubmitClaimToAPI({
  scenario,
  employeePoolFileName,
  employerPoolFileName,
  credentials,
  modifyClaim,
  logSubmissionToNewRelic = false,
  findCleanEmployee = true,
}: ClaimSubmissionOptions): void {
  const stashAndSubmit = (claim: DehydratedClaim) => {
    cy.stash("claim", claim);
    submitToAPI(claim, credentials, logSubmissionToNewRelic);
  };

  const modifyAndStash = (dehydratedClaim: DehydratedClaim) => {
    if (modifyClaim) {
      modifyClaim(dehydratedClaim).then(stashAndSubmit);
    } else {
      stashAndSubmit(dehydratedClaim);
    }
  };

  if (findCleanEmployee) {
    generateClaimForEmployeeWithoutClaims(
      scenario,
      employeePoolFileName,
      employerPoolFileName
    ).then((response) => modifyAndStash(response.claim));
  } else {
    generateClaim(scenario, employeePoolFileName, employerPoolFileName).then(
      modifyAndStash
    );
  }
}

export function submitToAPI(
  claim: DehydratedClaim,
  credentials?: Credentials,
  logSubmissionToNewRelic = false
): void {
  const timestamp_from = Date.now();
  cy.task("submitClaimToAPI", { ...claim, credentials }).then((res) => {
    const submission = {
      application_id: res.application_id,
      fineos_absence_id: res.fineos_absence_id,
    };

    cy.stash(
      "submission",
      { ...submission, timestamp_from },
      { logToNewRelic: false }
    );
    // TODO: Remove as part of https://lwd.atlassian.net/browse/PFMLPB-16452.
    // This double stash is a hack done to avoid sending the timestamp_from field to New Relic.
    // The above stash gets read by the spec that calls this function, while the second is only
    // read as metadata when the Cypress reporter is creating events to send to New Relic.
    // The name of the provided key doesn't matter since it shouldn't be read by Cypress.
    if (logSubmissionToNewRelic) {
      cy.stash("_submission", submission, {
        logToNewRelic: true,
      });
    }
  });
}

export function fillClaimantDOB(date_of_birth: string): void {
  const DOB = new Date(date_of_birth);
  cy.findAllByLabelText("Month").type(String(DOB.getMonth() + 1));
  cy.findAllByLabelText("Day").type(String(DOB.getUTCDate()));
  cy.findAllByLabelText("Year").type(String(DOB.getUTCFullYear()));
}

interface WaitForClaimSubmissionOptions {
  // This should be false only if a claim submission does not create an absence case in FINEOS.
  isCompleteClaim?: boolean;
  logSubmissionToNewRelic?: boolean;
}

/**
 * Waits for the claim submission response to come from the API.
 *
 * If an error is received during submission, it enriches the error with additional metadata.
 */
export function waitForClaimSubmission(
  options?: WaitForClaimSubmissionOptions
): Cypress.Chainable<{
  fineos_absence_id: string;
  application_id: string;
}> {
  return cy.wait("@submitClaimResponse").then((xhr) => {
    const isCompleteClaim = options?.isCompleteClaim ?? true;
    if (xhr.error) {
      throw new Error(`Error while waiting for claim submission: ${xhr.error}`);
    }
    if (!xhr.response) {
      throw new Error(
        "No response received while waiting for claim submission."
      );
    }

    if (xhr.response.statusCode < 200 || xhr.response.statusCode > 299) {
      const debugInfo: Record<string, string> = {
        ...extractDebugInfoFromHeaders(xhr.response?.headers ?? {}),
        ...extractDebugInfoFromBody(xhr.response?.body),
      };
      const debugInfoString = Object.entries(debugInfo)
        .map(([key, value]) => `${key}: ${value}`)
        .join("\n");
      throw new Error(
        `Application submission failed: ${xhr.request.url} - ${xhr.response.statusMessage} (${xhr.response.statusCode}\n\nDebug Information\n------------------\n${debugInfoString}`
      );
    }

    const body =
      typeof xhr.response.body === "string"
        ? JSON.parse(xhr.response.body)
        : xhr.response.body;

    if (
      !body.data.application_id ||
      (isCompleteClaim && !body.data.fineos_absence_id)
    ) {
      throw new Error(
        `Submission response is missing required properties: ${JSON.stringify(
          body
        )}`
      );
    }

    const submission = {
      fineos_absence_id: body.data.fineos_absence_id,
      application_id: body.data.application_id,
    };

    cy.stash(
      "submission",
      { ...submission, timestamp_from: Date.now() },
      { logToNewRelic: false }
    );
    // TODO: This is a hack to avoid sending timestamp_from to New Relic.
    // It should be removed as part of https://lwd.atlassian.net/browse/PFMLPB-16452.
    // The name of the provided key doesn't matter since it shouldn't be read by Cypress.
    const logSubmissionToNewRelic = options?.logSubmissionToNewRelic ?? false;
    if (logSubmissionToNewRelic) {
      cy.stash("_submission", submission, {
        logToNewRelic: true,
      });
    }

    maybeUpdateMMGProfile();
    return cy.wrap(submission, { log: false });
  });
}

/**
 * Delete the downloads folder to mitigate any file conflicts
 */
export function deleteDownloadsFolder(): void {
  cy.task("deleteDownloadFolder", Cypress.config("downloadsFolder"));
}

/**
 * Downloads Legal Notice based on type
 *
 * Also does basic assertion on contents of legal notice doc
 *
 * @param index - which notice to download with multiple notices of same type
 */
export function downloadLegalNotice(
  noticeType: NoticeType,
  claim_id: string,
  index = 0
) {
  downloadAndUseNotice(noticeType, index, (pdf) => {
    const application_id_from_notice = email.getTextBetween(
      pdf.text,
      "Application ID:",
      "\n"
    );
    expect(
      application_id_from_notice,
      `The claim_id within the legal notice should be: ${application_id_from_notice}`
    ).to.equal(claim_id);
  });
}
/**
 * Downloads a notice and asserts that it contains expected content.
 */
export function downloadNoticeAndAssertContent(
  noticeType: NoticeType,
  expectedContent: string
) {
  downloadAndUseNotice(noticeType, 0, (pdf) => {
    //remove line breaks if text spans multiple lines
    const formattedPdfTxt = { ...pdf }.text.replace(/\n/g, "");
    expect(
      formattedPdfTxt,
      `The notice should contain the content: ${expectedContent}`
    ).to.contain(expectedContent);
  });
}

/**
 * Downloads Legal Notice based on type this can be used for a subcase (e.g. Appeals)
 *
 * Also does basic assertion on contents of legal notice doc
 */
export function downloadLegalNoticeSubcase(
  sub_case: string,
  noticeType: NoticeType
): void {
  downloadAndUseNotice(noticeType, 0, (pdf) => {
    const application_id_from_notice = email.getTextBetween(
      pdf.text,
      "Application ID:",
      "\n"
    );
    expect(
      application_id_from_notice,
      `The claim_id within the legal notice should be: ${application_id_from_notice}`
    ).to.equal(sub_case);
  });
}

function downloadAndUseNotice(
  noticeType: NoticeType,
  index: number,
  useNotice: (pdf: PdfParse.Result) => void
) {
  cy.findAllByText(noticeType).eq(index).click();
  const downloadsFolder = Cypress.config("downloadsFolder");
  const downloadedNotice = path.join(downloadsFolder, `${noticeType}.pdf`);

  cy.task("waitForFileToBeReadable", downloadedNotice).then(() => {
    cy.task("getParsedPDF", downloadedNotice).then(useNotice);
  });
}

export function loginClaimant(credentials = getClaimantCredentials()) {
  cy.session(credentials.username, () => {
    cy.task("completeClaimantSsoLogin", credentials).then(
      setPortalOAuthAndFeatureFlags
    );
  });

  cy.visit("/");
}

export function startApplicationAndVerifyIdentity(
  claim: ApplicationRequestBody,
  credentials = getClaimantCredentials()
) {
  return cy.task("startApplicationAndIDV", { claim, credentials });
}

export function loginLeaveAdmin(accessKey: string | Credentials) {
  const credentials =
    typeof accessKey == "string"
      ? getLeaveAdminCredentials(accessKey)
      : accessKey;

  cy.session(credentials.username, () => {
    cy.task("completeLeaveAdminSsoLogin", credentials).then(
      setPortalOAuthAndFeatureFlags
    );
  });

  cy.visit("/");
}

function setPortalOAuthAndFeatureFlags(oauthState: string) {
  cy.window().then((window) => {
    cy.visit("/");
    window.localStorage.setItem("OAUTH_STATE", oauthState);
    // It’s important that setFeatureFlags() is called inside of this
    // callback, otherwise they will not be captured in the session.
    setFeatureFlags();
  });
}

export function goToSettingsPage(): void {
  cy.contains("a", "Settings").click({ force: true });
  cy.url().should("contain", "/user/settings/");
}

export function goToWelcomePage(): void {
  cy.contains("a", "Welcome").click();
  cy.url().should("contain", "/employers/welcome");
}

export function logout(): void {
  cy.wait(150);
  waitForPageLoad();
  cy.contains("button", "Log out").click({ force: true });

  cy.url().should("contain", "/oauth-start");
}

export function assertLoggedIn(): void {
  cy.contains("button", "Log out").should("be.visible");
  cy.url().should("not.include", "login");
}

export function startClaim(): void {
  // This page can take a longer time to load, especially in portal_caring_continuous__checkOrgUnits.ts test
  cy.get('[href="/applications/start/"]', { timeout: 16_000 }).click();
  cy.findByText("I understand and agree").click();
  maybeAutofillMMGProfile();
  cy.location({ timeout: 30_000 }).should((location) => {
    expect(location.pathname, "Expect to be on the checklist page").to.equal(
      "/applications/checklist/"
    );
    expect(location.search, "Expect to have a claim ID").to.include("claim_id");
  });
}

export function startClaimAndSubmitClaimPartOne(
  application: ApplicationRequestBody,
  options: SubmitClaimPartOneOptions = {},
  employer?: Employer
): void {
  if (hasProfileIdvFeature) {
    startApplicationAndVerifyIdentity(application).then((claimId) => {
      cy.visit(`/applications/checklist/?claim_id=${claimId}`);
      submitClaimPartOne(application, options, employer);
    });
  } else {
    startClaim();
    submitClaimPartOne(application, options, employer);
  }
}

export function clickChecklistButton(label: string): void {
  cy.contains(RegExp(label))
    .parents(".display-flex.border-bottom.border-base-light.padding-y-3")
    .contains("a", "Start")
    .click();
}
export function uploadCertificationDoc(reason: string) {
  switch (reason) {
    case "Serious Health Condition - Employee": {
      cy.contains("Certification of Your Serious Health Condition").click();
      break;
    }
    case "Care for a Family Member": {
      cy.contains(
        "Certification of Your Family Member’s Serious Health Condition"
      ).click();
      break;
    }
  }
  cy.contains("Save and continue").click();
  cy.contains("I am uploading one complete document").click();
  cy.contains("Save and continue").click();
  addLeaveDocs(
    reason === "Serious Health Condition - Employee" ? "HCP" : "FOSTER"
  );
}

export function hasChecklistButton(
  label: string,
  paymentPreference: PaymentPreferenceRequestBody,
  is_withholding_tax: boolean
): void {
  cy.contains(RegExp(label))
    .parents(".display-flex.border-bottom.border-base-light.padding-y-3")
    .then((element) => {
      if (element.find("a:contains('Start')").length > 0) {
        clickChecklistButton(label);
        if (label === "Enter payment (method|information)") {
          addPaymentInfo(paymentPreference);
        } else if (label === "Enter tax withholding preference") {
          addWithholdingPreference(is_withholding_tax);
        }
        onPage("checklist");
      }
    });
}
function assertUploadDocumentLaterBannerIsVisible() {
  cy.contains("Upload document later and review application").should(
    "be.visible"
  );
}

function getLanguageLabel(language: NonNullable<Language>) {
  const languageMap: Record<NonNullable<Language>, string> = {
    English: "English",
    French: "Français",
    Portuguese: "Português (Brasil)",
    "Haitian Creole": "Kreyòl Ayisyen",
    Spanish: "Español",
    "Chinese (simplified)": "中文",
    Vietnamese: "Tiếng Việt",
    "Language not listed": "Language not listed above",
  };
  return languageMap[language];
}

export function verifyIdentity(
  application: ApplicationRequestBody,
  options?: SubmitClaimPartOneOptions
): void {
  cy.findByLabelText("First name").type(application.first_name as string);
  cy.findByLabelText("Last name").type(application.last_name as string);
  cy.log("Employer FEIN", application.employer_fein);
  cy.contains("button", "Save and continue").click();

  cy.contains("fieldset", "What’s your date of birth?").within(() => {
    fillClaimantDOB(application.date_of_birth as string);
  });
  cy.contains("button", "Save and continue").click();

  // Added Phone Section behind Feature Flag
  cy.findByLabelText("Phone number").type(
    application.phone?.phone_number as string
  );
  // Answers Number Type
  cy.get(":nth-child(2) > .usa-radio__label").click();
  cy.contains("button", "Save and continue").click();

  cy.findByLabelText("Address").type(
    (application.mailing_address &&
      application.mailing_address.line_1) as string
  );
  cy.findByLabelText("City").type(
    (application.mailing_address && application.mailing_address.city) as string
  );
  cy.findByLabelText("State")
    .get("select")
    .select(
      (application.mailing_address &&
        application.mailing_address.state) as string
    );
  cy.findByLabelText("ZIP").type(
    (application.mailing_address && application.mailing_address.zip) as string
  );

  // Answers the question "Do you get your mail at this address?"
  cy.get(":nth-child(1) > .usa-radio__label").click();

  cy.contains("button", "Validate Address").click();
  cy.wait("@addressSearch");

  // E2E claims use fake address data, and will never pass Experian validation.
  // To get around this, we just continue with the invalid address.
  cy.contains("button", "Continue anyway").click();

  cy.contains(
    "In what language would you like to receive future written communications?"
  );

  if (options?.attemptNotPickingLanguage) {
    cy.contains("button", "Save and continue").click();

    cy.contains(
      ".usa-alert--error",
      "Select a language for future written communications."
    );
  }

  cy.contains(
    "form",
    "In what language would you like to receive future written communications?"
  ).within(() => {
    const languageLabel = getLanguageLabel(application.language || "English");
    cy.findByLabelText(languageLabel).clickAction();
    cy.contains("button", "Save and continue").click();
  });

  const fieldset = cy
    .contains("Do you have a Massachusetts driver’s license or ID card?")
    .parent();
  if (application.has_state_id) {
    fieldset.contains("label", "Yes").click();
    cy.contains("Enter your license or ID number").type(
      `{selectall}{backspace}${application.mass_id}`
    );
  } else {
    fieldset.contains("label", "No").click();
  }
  cy.contains("button", "Save and continue").click();

  cy.contains("What’s your Social Security Number?").type(
    `{selectall}{backspace}${application.tax_identifier}`
  );
  cy.contains("button", "Save and continue").click();

  cy.get("[data-cy='gender-form']").within(() => {
    cy.findByLabelText(application.gender as string).check();
    cy.contains("button", "Save and continue").click();
  });
  cy.get("[data-cy='ethnicity-form']").within(() => {
    inFieldsetLabelled("What is your ethnicity?", () => {
      cy.get("input[type='radio']").clickAction(
        application.ethnicity as string
      );
    });
    cy.contains("button", "Save and continue").click();
  });
  cy.get("[data-cy='race-form']").within(() => {
    inFieldsetLabelled("What is your race?", () => {
      cy.get("input[type='radio']").clickAction(
        "Another race not listed above"
      );
    });
    cy.contains(
      "If you identify as more than one race, you are welcome to write them all out."
    ).should("be.visible");
    inFieldsetLabelled("What is your race?", () => {
      cy.get("input[type='radio']").clickAction(application.race as string);
    });
    cy.contains("button", "Save and continue").click();
  });
}

export function selectClaimType(application: ApplicationRequestBody): void {
  // Preceeded by - "I am on the claims Checklist page";
  // Preceeded by - "I click on the checklist button called {string}"
  //                with the label "Enter leave details"
  const reason = application.leave_details?.reason;
  // @note: Claim generation doesn't support the other leave reasons specified in `ReasonQualifierOne`.
  // The type BaseClaimSpecification["reason_qualifier"] is used here since the claims
  // we generate won't contain other Reason Qualifiers other than "Newborn", "Adoption", and "Foster Care"
  const reasonQualifier = application.leave_details
    ?.reason_qualifier as BaseClaimSpecification["reason_qualifier"];

  const reasonMap: Record<string, string | RegExp> = {
    "Serious Health Condition - Employee":
      /I (can’t work due to my illness or injury.|need medical leave due to my illness or injury.)/,
    "Child Bonding":
      /I need (to bond|family leave to bond) with my child after birth, adoption, or foster placement./,
    "Pregnancy/Maternity":
      /I (can’t work due to my illness, injury, or pregnancy.|need medical leave to recover from giving birth or due to my inability to work during pregnancy.)/,
    "Care for a Family Member":
      /I need (to care|family leave to care) for my family member/,
  };

  if (!reason || !reasonMap[reason]) {
    throw new Error("Claim is missing reason or reason qualifier");
  }

  cy.contains(reasonMap[reason]).click();
  if (reasonQualifier) {
    const reasonQualifierMap: Record<typeof reasonQualifier, string> = {
      Newborn: "Birth",
      Adoption: "Adoption",
      "Foster Care": "Foster placement",
    };
    if (!(reasonQualifier in reasonQualifierMap)) {
      throw new Error(`Unknown reason qualifier: ${reasonQualifier}`);
    }
    cy.contains(reasonQualifierMap[reasonQualifier]).click();
  }
  cy.contains("button", "Save and continue").click();
}

export function answerPregnancyQuestion(
  application: ApplicationRequestBody
): void {
  // Example of selecting a radio button pertaining to a particular question. Scopes the lookup
  // of the "yes" value so we don't select "yes" for the wrong question.
  cy.contains(
    "fieldset",
    /Are you taking( medical)? leave because you are pregnant or (recently gave birth|recovering from (child)?birth)\?/
  ).within(() => {
    cy.contains(
      application.leave_details?.pregnant_or_recent_birth ? "Yes" : "No"
    ).click();
  });
  cy.contains("button", "Save and continue").click();
}

export function answerContinuousLeaveQuestion(
  application: ApplicationRequestBody
): void {
  if (!application.leave_details) {
    throw new Error("Leave details not provided.");
  }

  const leave = application.leave_details.continuous_leave_periods;

  cy.contains(
    "fieldset",
    "Do you need to take off work completely for a period of time (continuous leave)?"
  ).within(() => {
    cy.get("input[type='radio']").clickAction(
      application.has_continuous_leave_periods?.toString() as string
    );
  });

  if (application.has_continuous_leave_periods) {
    onPage("leave-period-continuous");
    completeDateForm(leave?.[0]?.start_date, leave?.[0]?.end_date);
  }
  cy.contains("button", "Save and continue").click();
}

export function answerReducedLeaveQuestion(
  application: ApplicationRequestBody
): void {
  if (!application.leave_details) {
    throw new Error("Leave details not provided.");
  }

  cy.contains(
    "fieldset",
    "Do you need to work fewer hours than usual for a period of time (reduced leave schedule)?",
    { timeout: 15_000 }
  ).within(() => {
    cy.get("input[type='radio']").clickAction(
      application.has_reduced_schedule_leave_periods?.toString() as string
    );
  });

  if (application.has_reduced_schedule_leave_periods) {
    const leave = application.leave_details.reduced_schedule_leave_periods;
    onPage("leave-period-reduced-schedule");
    completeDateForm(leave?.[0]?.start_date, leave?.[0]?.end_date);
    cy.contains("button", "Save and continue").click();
    enterReducedWorkHours(leave as ReducedScheduleLeavePeriods[]);
  } else {
    cy.contains("button", "Save and continue").click();
  }
}

export function enterReducedWorkHours(
  leave?: ReducedScheduleLeavePeriods[]
): void {
  if (!leave) {
    throw new Error(
      "Unable to enter reduced work hours - leave was not defined"
    );
  }
  const hrs = (minutes: number | null | undefined) => {
    return minutes ? Math.round(minutes / 60) : 0;
  };
  const weekdayInfo = [
    { day: "Sunday", hours: hrs(leave[0].sunday_off_minutes) },
    { day: "Monday", hours: hrs(leave[0].monday_off_minutes) },
    { day: "Tuesday", hours: hrs(leave[0].tuesday_off_minutes) },
    { day: "Wednesday", hours: hrs(leave[0].wednesday_off_minutes) },
    { day: "Thursday", hours: hrs(leave[0].thursday_off_minutes) },
    { day: "Friday", hours: hrs(leave[0].friday_off_minutes) },
    { day: "Saturday", hours: hrs(leave[0].saturday_off_minutes) },
  ];

  for (const info of weekdayInfo) {
    cy.contains("fieldset", info.day).within(() => {
      cy.findByLabelText("Hours").type(info.hours.toString());
    });
  }
  cy.contains("button", "Save and continue").click();
}

export function answerIntermittentLeaveQuestion(
  application: ApplicationRequestBody
): void {
  if (!application.leave_details) {
    throw new Error("Leave details not provided.");
  }

  cy.contains(
    "fieldset",
    "Do you need to take off work in uneven blocks of time (intermittent leave)?"
  ).within(() => {
    cy.get("input[type='radio']").clickAction(
      application.has_intermittent_leave_periods?.toString() as string
    );
  });
  const leave = application.leave_details?.intermittent_leave_periods?.[0];
  if (leave) {
    onPage("leave-period-intermittent");
    completeDateForm(leave.start_date, leave.end_date);
    cy.contains("button", "Save and continue").click();
    completeIntermittentLeaveDetails(leave);
  } else {
    cy.contains("button", "Save and continue").click();
  }
}

export function enterEmployerInfo(
  application: ApplicationRequestBody,
  options: SubmitClaimPartOneOptions = {
    concurrentEmploymentValidation: false,
    workPatternType: "Fixed",
  },
  employer?: Employer
): void {
  const {
    orgUnit,
    userNotFound,
    concurrentEmploymentValidation,
    workPatternType,
  } = options;
  // Preceeded by - "I am on the claims Checklist page";
  // Preceeded by - "I click on the checklist button called {string}"
  //                with the label "Enter employment information"
  if (application.employment_status === "Employed") {
    cy.findByLabelText("Enter your Employer Identification Number (EIN)").type(
      application.employer_fein as string
    );
  }

  cy.contains("button", "Save and continue").click();
  if (userNotFound === true) {
    cy.findByLabelText("Social Security Number").type(
      application.tax_identifier + ""
    );
    cy.findByLabelText("Employer Identification Number (EIN)").type(
      application.employer_fein + ""
    );
    cy.contains("button", "Save and continue").click();
    cy.findByText("I understand").click();
    cy.findAllByLabelText("What is your employer name?").type(
      // This isn't a great solution, but there's not a good way to get the employer name at runtime.
      // This helps us find logs in new relic particular to E2E applications in other E2E tests
      "[Assume this matches claim]"
    );
    cy.contains("button", "Save and continue").click();

    const dateOfBirth = new Date(application.date_of_birth as string);

    cy.findAllByLabelText("Month").type(
      (1 + dateOfBirth.getMonth()).toString()
    );
    cy.findAllByLabelText("Day").type(dateOfBirth.getDate().toString());
    cy.findAllByLabelText("Year").type(dateOfBirth.getFullYear().toString());
    cy.contains("button", "Save and continue").click();
    cy.get(":nth-child(1) > .usa-radio__label").click();
    cy.contains("button", "Save and continue").click();
  }

  if (orgUnit) {
    // This workflow depends on if the Claimant has been used beforehand.
    cy.findByText(/(Select|Confirm) your department/).then(($el) => {
      if (orgUnit === "I'm not sure" || orgUnit === "Blank") {
        if ($el.text().includes("Confirm your department")) {
          // Claimant is already linked to a Dept/OrgUnit, so select "No"
          cy.contains("label", "No").click();
        }
        if (orgUnit === "I'm not sure") {
          // Select 'I'm not sure' from the drop down
          cy.findByLabelText("Select your department")
            .get("select")
            .select(orgUnit, { force: true });
        }
        if (orgUnit === "Blank") {
          // Select the blank option from the drop down
          cy.findByLabelText("Select your department")
            .get("select")
            .select("", { force: true });
        }
      } else {
        if ($el.text().includes("Confirm your department")) {
          // Claimant already has been used and already linked to a Dept/OrgUnit.
          cy.contains(orgUnit);
          cy.contains("label", "Yes").click();
        } // This workflow depends on if the Claimant has been used beforehand.
        if ($el.text().includes("Confirm your department")) {
          // Claimant already has been used and already linked to a Dept/OrgUnit.
          cy.contains(orgUnit);
          cy.contains("label", "Yes").click();
          cy.contains("button", "Save and continue").click();
        } else {
          // Claimant never been used so we are select a specific Dept/OrgUnit to check the results
          // in FINEOS Absence Claim.
          cy.findByLabelText("Select your department")
            .get("select")
            .select(orgUnit, { force: true });
          cy.contains("button", "Save and continue").click();
        }
      }
      cy.contains("button", "Save and continue").click();
    });
  }

  if (application.employment_status === "Employed") {
    const employerDba = renderEmployerInfo(employer, options.userNotFound);

    // /occupation page
    // Applicant sees new page and content for occupation data collection
    cy.contains("h2", `What do you do for work at ${employerDba}?`).should(
      "be.visible"
    );
    cy.contains(
      "h3",
      "This information helps us understand the people and industries that use PFML to continue improving our website. Your benefits won't be impacted by your response. You can select an option that best matches what you do for work."
    ).should("be.visible");

    // Applicant required to respond
    cy.contains("button", "Save and continue").click();
    cy.get(".usa-alert--error").should("be.visible");
    cy.get(".usa-alert--error").contains("An error occurred");

    // Applicant sees combobox content for occupation data collection
    const industryList = [
      "Agriculture, Forestry, Fishing, and Hunting",
      "Utilities",
      "Construction",
      "Wholesale Trade",
      "Information",
      "Finance and Insurance",
      "Real Estate Rental and Leasing",
      "Professional, Scientific, and Technical Services",
      "Management of Companies and Enterprises",
      "Administrative and Support and Waste Management Remediation Services",
      "Educational Services",
      "Health Care and Social Assistance",
      "Arts, Entertainment, and Recreation",
      "Accommodation and Food Services",
      "Other Services (except Public Administration)",
      "Public Administration",
      "Manufacturing",
      "Retail Trade",
      "Transportation and Warehousing",
    ];
    const actualIndustryList: string[] = [];
    cy.get(".usa-combo-box").click();
    cy.get(".usa-combo-box__list li").each(($li) =>
      actualIndustryList.push($li.text())
    );
    cy.wrap(actualIndustryList.sort()).should(
      "deep.equal",
      industryList.sort()
    );

    // Applicant entered data persists
    // Click to open menu, select, open menu again, check selected option is still selected, click to close menu
    cy.get(".usa-combo-box").click();
    cy.get(".usa-combo-box__list li").first().click();
    cy.get(".usa-combo-box").click();
    cy.get(".usa-combo-box__list-option--selected").click();

    // Applicant can advance after answering question
    cy.contains("button", "Save and continue").click();

    if (userNotFound) {
      cy.get(".usa-label").should(
        "contain.text",
        "Was your employer recently merged with or acquired by another company?"
      );
      cy.contains("button", "Save and continue").click();

      cy.contains(
        "Do you want us to withhold state and federal taxes from this paid leave benefit?"
      );
      cy.get(":nth-child(1) > .usa-radio__label").click();
      cy.contains("button", "Submit tax withholding preference").click();

      cy.contains("How do you want to get your weekly benefit?");
      cy.get(`:nth-child(3) > .usa-radio__label`).click();
      cy.contains("button", "Submit payment method").click();
    }

    // /notified-employer page
    cy.contains(
      "fieldset",
      `Have you told ${employerDba} that you are taking Massachusetts Paid Family and Medical Leave?`
    ).within(() => {
      cy.contains(
        "label",
        application.leave_details?.employer_notified ? "Yes" : "No"
      ).click();
    });
    if (application.employment_status && application.leave_details) {
      if (application.leave_details.employer_notified) {
        cy.contains("fieldset", "When did you tell them?").within(() => {
          const notificationDate = new Date(
            application.leave_details?.employer_notification_date as string
          );
          cy.findByLabelText("Month").type(
            (notificationDate.getMonth() + 1).toString() as string
          );
          cy.findByLabelText("Day").type(
            notificationDate.getUTCDate().toString() as string
          );
          cy.findByLabelText("Year").type(
            notificationDate.getUTCFullYear().toString() as string
          );
        });
      } else {
        cy.contains(
          "Your leave request may be delayed or denied if you do not notify your employer of your leave at least 30 days before the start date. If you are unable to notify your employer of your leave at least 30 days before the leave starts due to reasons beyond your control, notify your employer as soon as it is practical to avoid potential delays or denials."
        ).should("be.visible");
      }
    }

    cy.contains("button", "Save and continue").click();
    describeWorkSchedule(
      application,
      employer,
      workPatternType,
      options.userNotFound
    );

    describeConcurrentEmployment(application, concurrentEmploymentValidation);
  }
}

function renderEmployerInfo(
  employer: Employer | undefined,
  userNotFound?: boolean
) {
  const validDba = employer?.dba !== "EMPLOYER NOT FOUND" && employer?.dba;
  const validFein = employer?.fein;

  if (userNotFound) {
    return `your employer (EIN #${employer?.fein})`;
  }

  if (validDba && validFein) {
    return `${employer.dba} (EIN #${employer?.fein})`;
  }

  if (!validDba && validFein) {
    return `your employer (EIN #${employer?.fein})`;
  }

  return "your employer";
}

function describeConcurrentEmployment(
  application: ApplicationRequestBody,
  concurrentEmploymentValidation: boolean = false
): void {
  const { has_concurrent_employers, hours_worked_per_week_all_employers } =
    application;

  cy.contains("Do you currently work for any additional employers?");

  if (!has_concurrent_employers) {
    cy.contains("No").click();
    cy.contains("button", "Save and continue").click();
    return;
  }

  cy.contains("Yes").click();

  cy.contains(
    "applying for paid family or medical leave if you have multiple employers."
  );

  cy.contains(
    "How many total hours do you work each week on average across all of your employers?"
  );

  if (concurrentEmploymentValidation) {
    cy.contains("button", "Save and continue").click();
    cy.get(".usa-alert__heading")
      .should("be.visible")
      .contains("An error occurred");
  }

  if (!hours_worked_per_week_all_employers) {
    throw Error("Hours worked per week must be specified");
  }

  const minutesInHour = 60;
  const hours = Math.floor(hours_worked_per_week_all_employers);
  const minutes =
    (hours_worked_per_week_all_employers * minutesInHour) % minutesInHour;

  //Invoke error validation where the number hours worked per a week is less than hours worked at one employer.

  if (concurrentEmploymentValidation) {
    cy.get('input[name="hours"]').type("1");
    cy.get('select[name="minutes"]').type("0");

    cy.contains("button", "Save and continue").click();

    cy.contains("button", "Save and continue").click();
    cy.get(".usa-alert__heading")
      .should("be.visible")
      .contains("An error occurred");

    // Invoke error validation where the number hours worked per a week is greater than 168 hours.
    cy.get('input[name="hours"]').type(`{selectAll}{backspace}170`);

    cy.contains("button", "Save and continue").click();
    cy.get(".usa-alert__heading")
      .should("be.visible")
      .contains("An error occurred");
  }

  // Enter the total number of hours worked per week across all employers.
  cy.get('input[name="hours"]').type(`{selectAll}{backspace}${hours}`);
  cy.get('select[name="minutes"]').type(`{selectAll}{backspace}${minutes}`);

  cy.contains("button", "Save and continue").click();
}

export function describeWorkSchedule(
  application: ApplicationRequestBody,
  employer?: Employer,
  workPatternType: "Fixed" | "Rotating" | "Variable" = "Fixed",
  userNotFound?: boolean
): void {
  const workScheduleType: WorkPattern["work_pattern_type"] = workPatternType;
  const employerDba = renderEmployerInfo(employer, userNotFound);
  cy.contains(
    "fieldset",
    `How would you describe your work schedule at ${employerDba}?`
  ).within(() => {
    cy.get(`input[value = ${workScheduleType}]`).clickAction();
  });
  cy.contains("button", "Save and continue").click();

  if (!application?.work_pattern?.work_pattern_days) {
    throw new Error("Work pattern days must be specified");
  }
  const workSchedule: WorkPatternDay[] =
    application?.work_pattern?.work_pattern_days;

  if (workScheduleType === "Fixed") {
    for (const workDay of workSchedule) {
      if (!(typeof workDay?.minutes === "number") || !workDay?.day_of_week) {
        throw new Error("Minutes and day of week must be specified");
      }
      const [hours, minutes] = minutesToHoursAndMinutes(workDay.minutes);
      cy.contains("fieldset", workDay.day_of_week, { timeout: 15_000 }).within(
        () => {
          cy.findByLabelText("Hours").type(hours.toString());
          cy.findByLabelText("Minutes").select(minutes.toString());
        }
      );
    }
  } else {
    const totalWorkTime =
      workSchedule
        .map((day) => day.minutes)
        .reduce((prev, curr) => {
          const prevMinutes = prev ?? 0;
          const currMinutes = curr ?? 0;
          return prevMinutes + currMinutes;
        }, 0) || 0;

    const [hours, minutes] = minutesToHoursAndMinutes(totalWorkTime);

    cy.contains("fieldset", "Average weekly hours", { timeout: 15_000 }).within(
      () => {
        cy.findByLabelText("Hours").type(hours.toString());
        cy.findByLabelText("Minutes").select(minutes.toString());
      }
    );
  }

  cy.contains("button", "Save and continue").click();
}

export function addPaymentInfo(
  paymentPreference: PaymentPreferenceRequestBody
): void {
  // Preceeded by - "I am on the claims Checklist page";
  // Preceeded by - "I click on the checklist button called {string}"
  //                with the label "Add payment information"
  const { payment_method, account_number, routing_number, bank_account_type } =
    paymentPreference.payment_preference as PaymentPreference;

  inFieldsetLabelled("How do you want to get your weekly benefit?", () => {
    const paymentInfoLabel: Record<
      NonNullable<PaymentPreference["payment_method"]>,
      string
    > = {
      Check: "Paper check",
      "Elec Funds Transfer": "Direct deposit",
      "Prepaid Card": "Prepaid Debit Card",
    };
    if (payment_method) {
      cy.contains(paymentInfoLabel[payment_method]).click();
    }
  });
  switch (payment_method) {
    case "Elec Funds Transfer": {
      cy.findByLabelText("Routing number").type(routing_number as string);
      cy.findByLabelText("Account number").type(account_number as string);
      cy.get(".usa-form .usa-fieldset .usa-form-group label").then(
        (element) => {
          if (element.text().includes("type account number")) {
            cy.findByLabelText(/(Retype|Re-type) account number/).type(
              account_number as string
            );
          }
        }
      );

      inFieldsetLabelled("Account type", () => {
        cy.get("input[type='radio']").clickAction(bank_account_type as string);
      });
      break;
    }

    case "Prepaid Card": {
      // This opens a new window that doesn't appear to impact the running test.
      cy.findByText("Read Disclosure").click();
      break;
    }

    default: {
      throw new Error("Unknown payment method");
    }
  }
  cy.findByText("Submit payment method").click();
}

export function addId(hasMassId = false): void {
  const frontMA = `${getFormPath("license-MA")}.pdf`;
  const frontOOS = `${getFormPath("license-OOS")}.pdf`;
  const back = `${getFormPath("license-back")}.pdf`;

  if (hasMassId) {
    cy.findByLabelText("Choose front").selectFile(frontMA, { force: true });
    cy.findByLabelText("Choose back").selectFile(back, { force: true });
  } else {
    cy.findByLabelText("Choose files").selectFile(frontOOS, { force: true });
    cy.findByLabelText("Choose additional files").selectFile(back, {
      force: true,
    });
  }

  cy.findByText(/(Save and continue|Submit documents)/).click();
}

export function addLeaveDocs(leaveType: string): void {
  cy.findByLabelText(/Choose files?/).selectFile(
    getDocPath(`${leaveType}.pdf`),
    { force: true }
  );
  cy.contains("button", "Save and continue").click();
}

export function addMilitaryDocs(docType: string): void {
  cy.contains("How will you upload this document?");
  cy.get(":nth-child(2) > .usa-radio__label").click();
  cy.contains("button", "Save and continue").click();
  addLeaveDocs(docType);
}

export function enterBondingDateInfo(
  application: ApplicationRequestBody
): void {
  const dateType =
    application.leave_details && application.leave_details.reason_qualifier;
  switch (dateType) {
    case "Newborn": {
      cy.contains("fieldset", "When was your child born?").within(() => {
        const DOB = new Date(
          application.leave_details?.child_birth_date as string
        );

        cy.contains("Month").type(String(DOB.getMonth() + 1) as string);
        cy.contains("Day").type(String(DOB.getUTCDate()) as string);
        cy.contains("Year").type(String(DOB.getUTCFullYear()) as string);
      });
      break;
    }

    case "Foster Care":
    case "Adoption": {
      inFieldsetLabelled(
        "When did the child arrive in your home through foster care or adoption?",
        () => {
          const DOB = new Date(
            application.leave_details?.child_placement_date as string
          );

          cy.contains("Month").type(String(DOB.getMonth() + 1) as string);
          cy.contains("Day").type(String(DOB.getUTCDate()) as string);
          cy.contains("Year").type(String(DOB.getUTCFullYear()) as string);
        }
      );
      break;
    }

    default: {
      throw new Error(`Unknown Reason Qualifier ${dateType}`);
    }
  }
  cy.contains("button", "Save and continue").click();
}

export function reviewAndSubmit(): void {
  cy.contains("a", "Review and submit application")
    .should("not.be.disabled")
    .click();
}

export function confirmSubmit(): void {
  // Usually preceeded by - "I am on the claims Confirm page"
  cy.contains("Submit application").click();
  cy.url({ timeout: 20_000 }).should("include", "/applications/success");
}

export function goToDashboardFromApplicationsPage(): void {
  cy.contains("Start a new application", {
    timeout: 15_000, // Dashboard can take awhile to load due to the number of claims the E2E user has
  }).click();
}

export function goToDashboardFromSuccessPage(): void {
  cy.get('a[href="/applications/"]').click();
}

export function confirmClaimSubmissionSucces(): void {
  cy.url().should("include", "/applications/success");
}

export function viewClaim(applicationId: string): void {
  cy.visit(`/applications/checklist/?claim_id=${applicationId}`);
  cy.url().should(
    "include",
    `/applications/checklist/?claim_id=${applicationId}`
  );
}

export function viewClaimStatus(absenceId: string): void {
  cy.visit(`/applications/status/?absence_id=${absenceId}`);
  cy.url().should("include", `/applications/status/?absence_id=${absenceId}`);
}

export function completeDateForm(
  startDate?: string | null,
  endDate?: string | null
): void {
  if (!startDate || !endDate) {
    throw new Error("Unable to fill in empty dates.");
  }

  const [startYear, startMonth, startDay] = startDate.split("-");
  const [endYear, endMonth, endDay] = endDate.split("-");
  cy.contains("fieldset", "First day of leave").within(() => {
    cy.contains("Month").type(startMonth);
    cy.contains("Day").type(startDay);
    cy.contains("Year").type(startYear);
  });
  cy.contains("fieldset", "Last day of leave").within(() => {
    cy.contains("Month").type(endMonth);
    cy.contains("Day").type(endDay);
    cy.contains("Year").type(endYear);
  });
}

export function completeIntermittentLeaveDetails(
  leave: IntermittentLeavePeriods
): void {
  cy.contains("fieldset", "How often might you need").within(() => {
    cy.get(
      `input[name='leave_details.intermittent_leave_periods[0].frequency_interval_basis'][value="${leave.frequency_interval_basis}"]`
    ).clickAction();
  });
  if (!leave.frequency) {
    throw new Error("Frequency must be specified");
  }
  cy.get(
    "input[name='leave_details.intermittent_leave_periods[0].frequency']"
  ).type(leave.frequency.toString());

  cy.contains("fieldset", "How long will an absence typically last?").within(
    () => {
      if (leave.duration_basis !== "Days") {
        throw new Error("Duration basis should be Days");
      }
      cy.findByLabelText("At least one day").clickAction();
    }
  );
  if (!leave.duration) {
    throw new Error("Frequency must be specified");
  }
  cy.get(
    "input[name='leave_details.intermittent_leave_periods[0].duration']"
  ).type(leave.duration.toString());

  cy.contains("button", "Save and continue").click();
}

export function checkHoursPerWeekLeaveAdmin(hwpw: number): void {
  cy.get("#employer-review-form").should((textArea) => {
    expect(
      textArea,
      `Hours worked per week should be: ${hwpw} hours`
    ).contain.text(String(hwpw));
  });
}

export function visitActionRequiredERFormPage(
  fineosAbsenceId: string,
  useDashboardReviewButton?: boolean
): void {
  if (useDashboardReviewButton) {
    cy.contains("[data-label='Application ID']", fineosAbsenceId)
      .parent()
      .within(() => {
        cy.contains("Review Application").click();
      });
  } else {
    cy.visit(`/employers/applications/review/?absence_id=${fineosAbsenceId}`);
  }
  cy.wait("@currentUser").wait(200);

  cy.wait("@review").wait(150);
  cy.wait("@documentsReview").wait(150);
  cy.contains("span", fineosAbsenceId);
  cy.url().should("contain", "/employers/applications/review");
}

interface RespondToLeaveAdminRequestOptions {
  readonly approval: boolean;
  readonly gaveNotice: boolean;
  // suspectFraud is currently unused, but is kept as an option for easy use in the future.
  readonly suspectFraud?: boolean;
  readonly isCaringLeave?: boolean;
}

export function respondToLeaveAdminRequest({
  approval,
  gaveNotice,
  suspectFraud,
  isCaringLeave,
}: RespondToLeaveAdminRequestOptions): void {
  cy.contains(
    "label",
    "Select this box to confirm you've reviewed the previous leave data."
  ).click();
  cy.contains(
    "label",
    "Select this box to confirm you've reviewed the other disability, family, or medical leave benefits data."
  ).click();

  cy.contains(
    "fieldset",
    "Do you have any reason to suspect this is fraud?"
  ).within(() => {
    cy.contains("label", suspectFraud ? "Yes (explain below)" : "No").click();
  });
  cy.contains(
    "fieldset",
    "Did the employee give you at least 30 days notice about their leave?"
  ).within(() => {
    cy.contains("label", gaveNotice ? "Yes" : "No (explain below)").click();
  });
  cy.contains(
    "fieldset",
    "Have you approved or denied this leave request?"
  ).within(() => {
    cy.contains("label", approval ? "Approve" : "Deny (explain below)").click();
  });
  // caring leave type has an additional question to respond to
  if (isCaringLeave) {
    cy.contains(
      "fieldset",
      "Do you believe the listed relationship is described accurately? (Optional)"
    ).within(() => {
      cy.contains("label", approval ? "Yes" : "No").click();
      cy.wait(150);
    });
    if (!approval) {
      cy.get('textarea[name="relationshipInaccurateReason"]').type(
        "Employee and person receiving care are not related"
      );
    }
  }
  if (suspectFraud || !gaveNotice || !approval) {
    cy.get('textarea[name="comment"]').type(
      "This is a generic explanation of the leave admin's response."
    );
  }

  cy.contains("button", "Submit").click();

  // This step can take a while.
  cy.contains("Thanks for reviewing the application", { timeout: 30_000 });
}

// @TODO PFMLPB-17392: Remove the following notice types:
// "Overpayment Payoff Notice (PDF)"
// "Payment Received-Updated Overpayment Balance (PDF)"
export type NoticeType =
  | "Approval notice (PDF)"
  | "Appeal Acknowledgment (PDF)"
  | "Benefit Amount Change Notice (PDF)"
  | "Maximum Weekly Benefit Change Notice (PDF)"
  | "Leave Allotment Change Notice (PDF)"
  | "Request for More Information (PDF)"
  | "Overpayment Payoff Notice (PDF)"
  | "Overpayment Notice (PDF)"
  | "Overpayment Notice - Full Balance Demand (PDF)"
  | "Overpayment Notice - Full Balance Recovery (PDF)"
  | "Overpayment Notice - Partial Recovery and Remaining Balance (PDF)"
  | "Appeal Dismissed - Exempt Employer (PDF)"
  | "Appeal Dismissed - Other (PDF)"
  | "Modify Decision (PDF)"
  | "Appeal Hearing Virtual Fillable (PDF)"
  | "Appeal - Returned To Adjudication (PDF)"
  | "Appeal Withdrawn (PDF)"
  | "Denial Notice Explanation of Wages (PDF)"
  | "Payment Received-Updated Overpayment Balance (PDF)"
  | "Explanation of Wages (PDF)"
  | "Pending Application Withdrawn (PDF)"
  | "Denial Notice (PDF)"
  | "Approval of Application Change (PDF)"
  | "Denial of Application Change (PDF)"
  | "Approved Leave Dates Cancelled (PDF)"
  | "Denial of Application (PDF)"
  | "Overpayment Payment Received (PDF)"
  | "Overpayment Paid in Full (PDF)"
  | "EFT Change Request (PDF)"
  | "Dismissal for Failure to Attend Hearing (PDF)"
  // Below notices are new in FR24-4.
  | "Appeal Postponement Agency (PDF)"
  | "Appeal Postponement Approved (PDF)"
  | "Appeal Postponement Denied (PDF)"
  | "Appeal Reinstatement Denied (PDF)"
  | "Appeal Reinstatement Granted (PDF)"
  | "Notice of Default (PDF)"
  | "Child Support (PDF)";

export function checkNoticeForLeaveAdmin(
  claimantName: string,
  noticeType: NoticeType
): void {
  cy.contains("h1", claimantName, { timeout: 25_000 });
  cy.findByText(`${noticeType}`).eq(0).should("be.visible");
}

/**
 * Asserts that a claimant can see any number of notices of the same type.
 *
 * Defaults to checking for one notice.
 */
export function checkNoticeForClaimant(noticeType: NoticeType, count = 1) {
  cy.findByTestId("view-your-notices").within(() =>
    cy
      .findAllByText(noticeType, { timeout: 25_000 })
      .should("have.length", count)
      .should("be.visible")
  );
}

export function getAllNotices() {
  const allNotices: string[] = [];
  // eslint-disable-next-line cypress/unsafe-to-chain-command
  return cy
    .findByTestId("view-your-notices")
    .within((notices) => {
      cy.get('button:contains("(PDF)")').each((notice) => {
        allNotices.push(notice.text());
      });

      // This implementation assumes that there the absence case has less than three pages of notices
      if (notices.find("[data-testid='pagination-navigation']").length > 0) {
        cy.get('[aria-label="Go to Next page"]').click();
        cy.get('button:contains("(PDF)")').each((notice) => {
          allNotices.push(notice.text());
        });
        cy.get('[aria-label="Go to Previous page"]').click();
      }
    })
    .then(() => allNotices);
}

export function confirmEligibleClaimant(): void {
  cy.findByText("I understand and agree").click();
}

interface SubmitClaimPartOneOptions {
  attemptNotPickingLanguage?: boolean;
  orgUnit?: string;
  otherLeavesAndBenefits?: ReportOtherLeavesAndBenefitsOptions;
  userNotFound?: boolean;
  concurrentEmploymentValidation?: boolean;
  stepsOneTwoComplete?: boolean;
  workPatternType?: "Fixed" | "Rotating" | "Variable";
}

export function submitClaimPartOne(
  application: ApplicationRequestBody,
  options: SubmitClaimPartOneOptions = {},
  employer?: Employer
): void {
  const { concurrentEmploymentValidation } = options;
  const reason = application.leave_details?.reason;
  if (!options.stepsOneTwoComplete) {
    if (!hasProfileIdvFeature) {
      clickChecklistButton("Verify your identification");
      verifyIdentity(application, options);
    }
    onPage("checklist");

    if (concurrentEmploymentValidation) {
      cy.contains("you must create separate applications for each job.");
    }
    clickChecklistButton("Enter employment information");
    enterEmployerInfo(application, options, employer);
  }
  onPage("checklist");
  clickChecklistButton("Enter leave details");
  selectClaimType(application);

  switch (reason) {
    case "Serious Health Condition - Employee":
    case "Pregnancy/Maternity": {
      break;
    }

    case "Care for a Family Member": {
      answerCaringLeaveQuestions(application);
      break;
    }
    default: {
      enterBondingDateInfo(application);
      break;
    }
  }

  answerContinuousLeaveQuestion(application);
  answerReducedLeaveQuestion(application);
  answerIntermittentLeaveQuestion(application);
  onPage("checklist");
  cy.get(
    "a[href*='/info-details/how-other-leave-and-benefits-can-affect-your-paid-family-and-medical-leave']"
  ).should("exist");
  clickChecklistButton("Report other leave, benefits, and income");
  reportOtherLeavesAndBenefits(application, options.otherLeavesAndBenefits);
  if (hasProfileIdvFeature) {
    clickChecklistButton("Confirm demographic details and preferences");
    answerGenderAndLanguage(application);
  }
  const buttonWording = hasProfileIdvFeature ? "submit" : "confirm";
  clickChecklistButton(`Review and ${buttonWording}`);
  if (reason === "Child Bonding" || reason === "Care for a Family Member") {
    confirmEligibleClaimant();
  }

  onPage("review");

  const languageLabel =
    application.language === "Language not listed"
      ? getLanguageLabel("English")
      : getLanguageLabel(application.language || "English");

  cy.contains("div", "Language for written communications").within(() => {
    cy.contains(languageLabel);
  });

  if (application.leave_details !== undefined) {
    validateAlertWithoutERNotification(
      application.leave_details.employer_notified
    );
  }
  cy.findByText("Submit Part 1").click();
}

export function answerCaringLeaveQuestions(
  application: ApplicationRequestBody
): void {
  cy.contains("I am caring for my sibling.").click();
  cy.contains("Save and continue").click();
  cy.findByLabelText("First name").type(
    application.leave_details?.caring_leave_metadata
      ?.family_member_first_name as string
  );
  cy.findByLabelText("Last name").type(
    application.leave_details?.caring_leave_metadata
      ?.family_member_first_name as string
  );
  cy.contains("Save and continue").click();
  const familyMemberDOB = new Date(
    application.leave_details?.caring_leave_metadata
      ?.family_member_date_of_birth as string
  );
  cy.findByLabelText("Month").type(
    (familyMemberDOB.getMonth() + 1).toString() as string
  );
  cy.findByLabelText("Day").type(
    familyMemberDOB.getUTCDate().toString() as string
  );
  cy.findByLabelText("Year").type(
    familyMemberDOB.getUTCFullYear().toString() as string
  );
  cy.contains("Save and continue").click();
}

export function submitPartsTwoThreeNoLeaveCert(
  paymentPreference: PaymentPreferenceRequestBody,
  is_withholding_tax: boolean,
  hasMassId = false
): void {
  clickChecklistButton("Enter payment (method|information)");
  addPaymentInfo(paymentPreference);
  onPage("checklist");
  clickChecklistButton("Enter tax withholding preference");
  addWithholdingPreference(is_withholding_tax);
  clickChecklistButton("Upload proof of identity");
  addId(hasMassId);
  onPage("checklist");
  cy.get(".border-red").should("not.exist");
}

export function submitChannelSwitchingClaimPartsTwoThree(
  application: ApplicationRequestBody,
  paymentPreference: PaymentPreferenceRequestBody,
  is_withholding_tax: boolean
): void {
  hasChecklistButton(
    "Enter payment (method|information)",
    paymentPreference,
    is_withholding_tax
  );
  hasChecklistButton(
    "Enter tax withholding preference",
    paymentPreference,
    is_withholding_tax
  );
  clickChecklistButton("Upload proof of identity");
  addId(!!application.mass_id);
  onPage("checklist");
  clickChecklistButton("Upload miliary exigency document");
  addMilitaryDocs("EXIGENCY");
  onPage("checklist");
  clickChecklistButton("Upload family member active duty service proof");
  addMilitaryDocs("MILPROOF");
  completeClaim({
    is_withholding_tax,
    industry_sector: application.industry_sector,
  });
}

export function submitClaimPartsTwoThree(
  application: ApplicationRequestBody,
  paymentPreference: PaymentPreferenceRequestBody,
  options: SubmitClaimPartThreeOptions,
  withCertificationDocument: boolean = true
): void {
  clickChecklistButton("Enter payment (method|information)");
  addPaymentInfo(paymentPreference);
  onPage("checklist");
  clickChecklistButton("Enter tax withholding preference");
  addWithholdingPreference(options.is_withholding_tax);

  // Pass withCertificationDocument through options if not already set
  const updatedOptions = {
    ...options,
    withCertificationDocument:
      options.withCertificationDocument ?? withCertificationDocument,
  };

  submitClaimPartThree(application, updatedOptions, withCertificationDocument);
}

interface SubmitClaimPartThreeOptions {
  alternateForm?: "Employer FMLA";
  is_withholding_tax: boolean;
  skipIndustryCheck?: boolean;
  withCertificationDocument?: boolean;
}

function getFormType(
  reason: string,
  alternateForm?: SubmitClaimPartThreeOptions["alternateForm"]
): string {
  if (alternateForm === "Employer FMLA") {
    return "My employer’s Family and Medical Leave Act (FMLA) form";
  }

  switch (reason) {
    case "Serious Health Condition - Employee": {
      return "Certification of Your Serious Health Condition form";
    }
    case "Care for a Family Member": {
      return "Certification of Your Family Member’s Serious Health Condition form";
    }
    default: {
      return "My employer’s Family and Medical Leave Act (FMLA) form";
    }
  }
}

function uploadDocumentFlowPartThree(
  reason: string,
  options?: SubmitClaimPartThreeOptions
) {
  if (
    reason === "Serious Health Condition - Employee" ||
    reason === "Care for a Family Member"
  ) {
    // Assert error notification message
    cy.contains("Save and continue").click();
    cy.get(".usa-alert__heading")
      .should("be.visible")
      .contains("An error occurred");
    cy.get(".usa-alert__text")
      .should("be.visible")
      .contains("Choose which type of form you're uploading");

    // Assert Upload options
    cy.contains("What type of form are you uploading?");

    const optionToSelect = getFormType(reason, options?.alternateForm);
    cy.findByLabelText(optionToSelect).click({ force: true });
    cy.contains("Save and continue").click();

    // Assert upload document function
    cy.contains("How will you upload this document?");

    // Bypass Upload guidance
    cy.get(":nth-child(2) > .usa-radio__label").click();
    cy.contains("Save and continue").click();
  }
}

function getDocName(reason?: ApplicationLeaveDetailsReason | null) {
  switch (reason) {
    case "Serious Health Condition - Employee": {
      return "HCP";
    }
    case "Care for a Family Member": {
      return "caring";
    }
    default: {
      return "FOSTER";
    }
  }
}

export function submitClaimPartThree(
  application: ApplicationRequestBody,
  options: SubmitClaimPartThreeOptions,
  withCertificationDocument: boolean = true
) {
  const { industry_sector, leave_details, mass_id } = application;
  const {
    is_withholding_tax,
    skipIndustryCheck,
    withCertificationDocument: optionsWithCertificationDocument,
  } = options;
  const isEmployerNotified = leave_details?.employer_notified !== false;

  // Use withCertificationDocument from options if provided, otherwise fall back to parameter
  const shouldUploadCertificationDocument =
    optionsWithCertificationDocument ?? withCertificationDocument;

  clickChecklistButton("Upload proof of identity");
  addId(!!mass_id);
  if (shouldUploadCertificationDocument) {
    const reason = leave_details?.reason;
    onPage("checklist");
    clickChecklistButton("Upload leave certification documents");
    if (shouldUploadCertificationDocument) {
      if (reason) {
        uploadDocumentFlowPartThree(reason, options);
      }

      addLeaveDocs(getDocName(reason));
    }
    completeClaim({
      is_withholding_tax,
      industry_sector,
      skipIndustryCheck,
      isEmployerNotified,
    });
  } else {
    submitClaimWithoutCertificationDoc();
  }
}

function submitClaimWithoutCertificationDoc() {
  clickChecklistButton("Upload leave certification documents");
  cy.findByLabelText("Another form of documentation").click({ force: true });
  cy.contains("button", "Save and continue").click();
  assertUploadDocumentLaterBannerIsVisible();
  cy.contains("a", "Upload document later and review application").click();
  cy.contains("h1", "Review and submit your application").should("be.visible");
  cy.contains("button", "Submit application").click();
}

export function uploadCertificationDocAsClaimant(
  reason: string,
  formText: string
) {
  cy.contains("Incomplete documentation").should("be.visible");
  cy.get("a").contains("View application").click();
  cy.contains(`Certification of ${formText} Form`).should("be.visible");

  cy.contains("Upload your certification form").click();
  uploadCertificationDoc(reason);
  cy.get('div[data-testid="alert-banner"]').should(
    "contain.text",
    "You've successfully submitted your certification form"
  );
  //assert document upload prompt (only present if no docs have been uploaded) is not present
  cy.contains("Upload Certification").should("not.exist");
  //assert first doc uploaded confirmation message is present
  cy.contains("Documents submitted").should("exist");
  //assert second doc uploaded confirmation message is present
  cy.get("[data-testid='documentsSubmitted']")
    .contains("Completed")
    .should("exist");
}

interface CompleteClaimOptions {
  is_withholding_tax: boolean;
  industry_sector?: industrySector;
  isEmployerNotified?: boolean;
  skipIndustryCheck?: boolean; // Claims created in FINEOS will not have industry data input
}

export function completeClaim(options: CompleteClaimOptions) {
  const {
    is_withholding_tax,
    industry_sector,
    isEmployerNotified,
    skipIndustryCheck,
  } = options;

  onPage("checklist");
  reviewAndSubmit();
  onPage("review");
  validateAlertWithoutERNotification(isEmployerNotified);
  cy.contains("Job type")
    .parent()
    .contains(
      skipIndustryCheck ? "Not answered" : industry_sector ?? "Unknown job type"
    )
    .should("be.visible");
  cy.contains("Withhold state and federal taxes?")
    .parent()
    .contains(is_withholding_tax ? "Yes" : "No")
    .should("be.visible");
  confirmSubmit();
  goToDashboardFromSuccessPage();
  cy.wait(3000);
}

export function verifyLeaveAdmin(withholding: number): void {
  cy.get('a[href="/employers/organizations"]').first().click();
  cy.get('a[href^="/employers/organizations/verify-contributions"]')
    .last()
    .click();
  cy.get('input[id^="InputText"]').type(withholding.toString());
  cy.get('button[type="submit"').click();
  cy.contains("h1", "Thanks for verifying your paid leave contributions", {
    timeout: 30_000,
  });
  cy.contains("p", "Your account has been verified");
  cy.contains(".usa-button", "Continue").click();
  cy.get('a[href^="/employers/organizations/verify-contributions"]').should(
    "not.exist"
  );
}

/**
 * Register and verify a leave admin for an organization.
 */
export function addOrganization(
  fein: string,
  withholding: number,
  mtc_number?: string
): void {
  cy.get('a[href="/employers/organizations/add-organization/"').click();
  cy.get('input[name="ein"]').type(fein);
  cy.get('button[type="submit"').click();

  if (withholding !== 0) {
    cy.get('input[name^="withholding"]', { timeout: 30_000 }).type(
      withholding.toString()
    );
    cy.get('button[type="submit"]').click();
  } else if (mtc_number) {
    cy.get('input[name^="mtc_number"]', { timeout: 30_000 }).type(mtc_number);
    cy.get('button[type="submit"]').click();
  } else {
    throw new Error(
      `Employer has no withholdings (fein: ${fein}), and no mtc_number provided.`
    );
  }

  cy.contains("h1", "You can now manage leave for this organization", {
    timeout: 30_000,
  });
  cy.contains(".usa-button", "Manage leave").click();
  cy.url().should("contain", "/employers/applications/");
  cy.get('a[href^="/employers/organizations/verify-contributions"]').should(
    "not.exist"
  );
}

export function selectClaimFromEmployerDashboard(
  fineosAbsenceId: string
): void {
  goToEmployerApplicationsPage();
  searchClaims(fineosAbsenceId);
  cy.findByText("View application").click();
}

export function assertClaimNotInEmployerDashboard(
  fineosAbsenceId: string
): void {
  goToEmployerApplicationsPage();
  findClaim(fineosAbsenceId);
  // Assert no claims were found
  cy.contains("0 results");
  cy.get("table tbody tr").contains("No applications on file");
}

export function assertLACannotAccessClaim(fineosAbsenseId: string): void {
  cy.visit(`/employers/applications/status/?absence_id=${fineosAbsenseId}`);
  cy.get(".usa-alert").contains(
    "Sorry, you do not have permission to view that page"
  );
}

export function assertUnverifiedEmployerDashboard(): void {
  cy.contains("Get access to your organization(s)");
  cy.contains("You have not verified any organizations.");
}

export function goToEmployerApplicationsPage(): void {
  cy.contains("a", "Applications").click();
}

export function goToOrganizationsTab(): void {
  cy.get('nav[aria-label="Employer menu"]').within(() => {
    cy.findByText(/(Organizations)/).click();
  });
}

export function assertLeaveDatesAsLA(startDate: string, endDate: string): void {
  cy.findByText("Leave duration").parent().contains(startDate);
  cy.findByText("Leave duration").parent().contains(endDate);
}
/**
 * Sequentially reports all of the given previous leaves.
 * Assumes browser is navigated to either
 * @param previous_leaves
 */
function reportPreviousLeaves(previous_leaves: ValidPreviousLeave[]): void {
  const lastLeaveIndex = previous_leaves.length - 1;

  previous_leaves.forEach((leave, index) => {
    reportPreviousLeave(leave, index);

    // if there are any more leaves to report - click the button
    if (index < lastLeaveIndex) {
      cy.contains("button", "Add another leave").click();
    }
  });

  cy.contains("button", "Save and continue").click();
}

/**
 * Map the leave reasons accepted by API to current text used by the portal.
 */
const leaveReasonMap = {
  "A health condition during pregnancy":
    "A health condition during pregnancy for which I received written certification from a health care provider.",
  "An illness or injury":
    "A health condition kept me out of work for at least 3 days for which I received written certification from a health care provider.",
  "An illness or injury that required hospitalization":
    "I received care in a hospital due to an illness or injury.",
  "Bonding with my child after birth or placement":
    "Bonding with my child after birth, adoption, or foster care placement as is certified by my proof of birth or placement documentation.",
  "Caring for a family member with a serious health condition":
    "Caring for a family member with a serious health condition that was certified in writing by a health care provider.",
  "Caring for a family member who serves in the armed forces":
    "Caring for a family member who was serving in the armed forces.",
  "Managing family affairs while a family member is on active duty in the armed forces":
    "Managing family affairs while a family member was on active duty in the armed forces.",
  Pregnancy:
    "I was recovering from giving birth as certified by my health care provider.",
  Unknown: "",
} as const;

/**
 * Fills out the previous leave form for the given leave.
 * @param leave valid previous leave object
 * @param index the number of the leave being reported
 */
function reportPreviousLeave(leave: ValidPreviousLeave, index: number) {
  const leaveNumber = index + 1;

  inFieldsetLabelled(`Leave ${leaveNumber}`, () => {
    cy.contains("label", leaveReasonMap[leave.leave_reason]).click();

    inFieldsetLabelled("Did you take leave from this employer?", () => {
      const label = leave.is_for_current_employer ? "Yes" : "No";
      cy.findByLabelText(label).clickAction();
    });

    fillDateFieldset(
      "What was the first day of this leave?",
      leave.leave_start_date
    );

    fillDateFieldset(
      "What was the last day of this leave?",
      leave.leave_end_date
    );

    inFieldsetLabelled(
      "Were you completely off work from the start to the end of this leave?",
      () => {
        const label = leave.is_continuous ? "Yes" : "No";
        cy.findByLabelText(label).clickAction();
      }
    );

    if (!leave.is_continuous) {
      inFieldsetLabelled(
        "How many hours per week did you normally work prior to taking this leave?",
        () => {
          const [hours, minutes] = minutesToHoursAndMinutes(
            leave.worked_per_week_minutes
          );
          cy.contains("Hours").type(hours.toString());
          cy.contains("Minutes").type(minutes.toString());
        }
      );

      inFieldsetLabelled(
        "What was the total number of hours you took off?",
        () => {
          const [hours, minutes] = minutesToHoursAndMinutes(
            leave.leave_minutes
          );
          cy.contains("Hours").type(hours.toString());
          cy.contains("Minutes").type(minutes.toString());
        }
      );
    }
  });
}

/**
 * Attempts to report invalid employer benefits. Must be navigated to the
 * employer benefits form.
 */
function attemptToReportInvalidEmployerBenefits(claim: ValidClaim) {
  const selector = (content: string) => content.startsWith("Yes");
  cy.findByLabelText(selector).clickAction();

  const [, leaveEnd] = getLeavePeriod(claim.leave_details).map((dateString) =>
    parseISO(dateString)
  );

  const [waitingPeriodStart, waitingPeriodEnd] = getWaitingPeriod(
    claim.leave_details
  );

  fillDateFieldset(
    "When will you start receiving your full wage payments?",
    formatDateAsIsoExt(waitingPeriodStart)
  );

  fillDateFieldset(
    "When will you stop receiving your full wage payments?",
    formatDateAsIsoExt(waitingPeriodEnd)
  );

  cy.contains("Save and continue").click();

  cy.contains(
    ".usa-alert--error",
    "Only report employer benefits you will be receiving on dates that fall after your 7 day waiting period. You do not need to report any employer benefits prior to or during your waiting period."
  );

  cy.contains(".usa-alert--error", "Select the kind of benefit you will use.");

  const oneDayAfterLeaveEnd = addDays(leaveEnd, 1);

  fillDateFieldset(
    "When will you start receiving your full wage payments?",
    formatDateAsIsoExt(oneDayAfterLeaveEnd)
  );

  const twoDaysAfterLeaveEnd = addDays(leaveEnd, 2);

  fillDateFieldset(
    "When will you stop receiving your full wage payments?",
    formatDateAsIsoExt(twoDaysAfterLeaveEnd)
  );

  cy.contains("Save and continue").click();
  const leaveEndText = formatDateAsText(leaveEnd);

  cy.contains(
    ".usa-alert--error",
    `You only need to report employer benefits you will be receiving before ${leaveEndText}. You do not need to report employer benefits after this date.`
  );

  const dayAfterWaitingPeriod = addDays(waitingPeriodEnd, 1);
  const dayAfterWaitingPeriodText = formatDateAsText(dayAfterWaitingPeriod);

  cy.contains(
    ".usa-alert--error",
    `You only need to report employer benefits you will be receiving between ${dayAfterWaitingPeriodText} and ${leaveEndText}. You do not need to report employer benefits you receive after these dates.`
  );
}

function getBenefitTypeLabel(
  benefitType: ValidEmployerBenefit["benefit_type"]
) {
  const benefitTypeLabelMap: Record<
    ValidEmployerBenefit["benefit_type"],
    string | RegExp
  > = {
    "Family or medical leave insurance": "Family or medical leave insurance",
    "Paid time off": /Paid time off/,
    "Permanent disability insurance": "Permanent disability insurance",
    "Short-term disability insurance": /Temporary disability insurance.*/,
    "Accrued paid leave": "",
    Unknown: "I am not sure which benefit will fully cover my wages",
    "I'm not sure": "I'm not sure",
  };

  if (!supportedBenefitTypes.includes(benefitType)) {
    throw new Error(
      `Benefit type "${benefitType}" is not reportable through the portal ` +
        "web app."
    );
  }

  return benefitTypeLabelMap[benefitType];
}

const supportedBenefitTypes: readonly ValidEmployerBenefit["benefit_type"][] = [
  "Family or medical leave insurance",
  "Permanent disability insurance",
  "Short-term disability insurance",
  "Paid time off",
];

/**
 * Fills out the employer-sponsored benefits form for a given benefit.
 */
function reportEmployerBenefit(benefit: ReportableBenefit) {
  cy.findByLabelText(getBenefitTypeLabel(benefit.benefit_type)).click({
    force: true,
  });

  fillDateFieldset(
    "When will you start receiving your full wage payments?",
    benefit.benefit_start_date
  );

  fillDateFieldset(
    "When will you stop receiving your full wage payments?",
    benefit.benefit_end_date
  );
}

interface ReportableBenefit extends ValidEmployerBenefit {
  benefit_start_date: string;
  benefit_end_date: string;
  is_full_salary_continuous: true;
}

/**
 * Report employer benefits. Must be navigated to the employer benefits form.
 * @param benefits - array of EmployerBenefit objects with all the required properties.
 */
function reportEmployerBenefits(claim: ValidClaim) {
  const selector = (content: string) => content.startsWith("Yes");
  cy.findByLabelText(selector).clickAction();
  assertIsTypedArray(claim.employer_benefits, isValidEmployerBenefit);

  const potentialReportableBenefits = claim.employer_benefits.map((benefit) => {
    const period = getReportableBenefitPeriod(
      [benefit.benefit_start_date, benefit.benefit_end_date],
      claim.leave_details
    );

    const mappedBenefit: ValidEmployerBenefit = {
      ...benefit,
      benefit_end_date: period ? formatDateAsIsoExt(period[1]) : "",
      benefit_start_date: period ? formatDateAsIsoExt(period[0]) : "",
    };

    return mappedBenefit;
  });

  const reportableBenefits =
    potentialReportableBenefits.filter(isReportableBenefit);
  const lastReportableBenefitIndex = reportableBenefits.length - 1;

  reportableBenefits.forEach((benefit, index) => {
    cy.get(
      `fieldset:contains(Benefit received from your current employer EIN ${claim.employer_fein})`
    )
      .eq(index)
      .within(() => {
        reportEmployerBenefit(benefit);
      });

    // if there are any more benefits to report - click the button
    if (index < lastReportableBenefitIndex) {
      cy.contains("button", "Add another policy").click();
    }
  });

  cy.contains("button", "Save and continue").click();
}

function isReportableBenefit(
  benefit: ValidEmployerBenefit
): benefit is ReportableBenefit {
  return (
    !!benefit.benefit_end_date &&
    !!benefit.benefit_start_date &&
    benefit.is_full_salary_continuous
  );
}

const otherIncomeTypeMap: Record<ValidOtherIncome["income_type"], string> = {
  "Disability benefits under Gov't retirement plan":
    "Disability benefits under a governmental retirement plan",
  "Earnings from another employment/self-employment":
    "Earnings or benefits from another employer, or through self-employment",
  "Jones Act benefits": "Jones Act benefits",
  "Railroad Retirement benefits": "Railroad Retirement benefits",
  "Unemployment Insurance": "Unemployment Insurance",
  "Workers Compensation": "Workers Compensation",
  SSDI: "Social Security Disability Insurance",
  Unknown: "@TODO: Unknown Other Income not supported yet.",
};

/**
 * Fills out the other income form for a given income.
 * @param income
 * @param index
 */
function reportOtherIncome(income: ValidOtherIncome, index: number): void {
  inFieldsetLabelled(`Income ${index + 1}`, () => {
    inFieldsetLabelled("What kind of income is it?", () => {
      cy.contains("label", otherIncomeTypeMap[income.income_type]).click();
    });

    fillDateFieldset(
      "What is the first day of your leave that this income will pay you for?",
      income.income_start_date
    );
    fillDateFieldset(
      "What is the last day of your leave that this income will pay you for?",
      income.income_end_date
    );

    inFieldsetLabelled("How much will you receive?", () => {
      cy.findByLabelText("Amount").type(`${income.income_amount_dollars}`);

      const frequencyMap: Record<
        ValidOtherIncome["income_amount_frequency"],
        string
      > = {
        "In Total": "All at once",
        "Per Day": "Daily",
        "Per Week": "Weekly",
        "Per Month": "Monthly",
        Unknown: "@TODO: Unknown Other Income not supported yet.",
      };

      cy.findByLabelText("Frequency").select(
        frequencyMap[income.income_amount_frequency]
      );
    });
  });
}

/**
 * Report other incomes. Must be navigated to the other incomes form.
 * @param other_incomes - array of OtherIncome objects with all the required properties.
 */
("Tell us about your other sources of income during your leave dates for paid leave from PFML.");
function reportOtherIncomes(other_incomes: ValidOtherIncome[]): void {
  cy.contains(
    "form",
    "Tell us about your other sources of income during your leave dates for paid leave from PFML."
  ).within(() => {
    other_incomes.forEach((income, index) => {
      reportOtherIncome(income, index);
      // if there are any more leaves to report - click the button
      if (index < other_incomes.length - 1) {
        cy.contains("button", "Add another income").click();
      }
    });
    cy.contains("button", "Save and continue").click();
  });
}

interface ReportOtherLeavesAndBenefitsOptions {
  attemptInvalidEmployerBenefits?: boolean;
}

/**
 * Report all of the leaves and benefits within the claim.
 * Assumes starting at the first page of reporting other leaves & benefits.
 * @param claim
 */
function reportOtherLeavesAndBenefits(
  claim: ApplicationRequestBody,
  options: ReportOtherLeavesAndBenefitsOptions = {}
): void {
  assertValidClaim(claim);

  cy.contains("Save and continue").click();

  if (claim.previous_leaves?.length) {
    cy.findByLabelText((content: string) =>
      content.startsWith("Yes")
    ).clickAction();
    assertIsTypedArray(claim.previous_leaves, isValidPreviousLeave);
    reportPreviousLeaves(claim.previous_leaves);
  } else {
    cy.findByLabelText((content: string) =>
      content.startsWith("No")
    ).clickAction();
    cy.contains("button", "Save and continue").click();
  }

  cy.url().should("include", "paid-time-off-explainer");
  cy.contains("button", "Save and continue").click();

  cy.contains("form", getEmployerBenefitIntroPrompt(claim)).submit();

  if (options.attemptInvalidEmployerBenefits) {
    attemptToReportInvalidEmployerBenefits(claim);
  }

  cy.contains("form", getEmployerBenefitFormPrompt(claim)).within(() => {
    if (hasReportableEmployerBenefits(claim)) {
      reportEmployerBenefits(claim);
    } else {
      const selector = (content: string) => content.startsWith("No");
      cy.findByLabelText(selector).clickAction();
      cy.contains("Save and continue").click();
    }
  });

  cy.contains(
    "form",
    "Will you receive income from any other sources during your leave dates for paid leave?"
  ).within(() => {
    cy.findByLabelText(claim.other_incomes ? "Yes" : "No", {
      exact: false,
    }).clickAction();
    cy.contains("Save and continue").click();
  });

  // Tell us about your other sources of income during your leave dates for paid leave.
  if (claim.other_incomes?.length) {
    assertIsTypedArray(claim.other_incomes, isValidOtherIncome);
    reportOtherIncomes(claim.other_incomes);
  }
}

function hasReportableEmployerBenefits(claim: ValidClaim) {
  return (
    isTypedArray(claim.employer_benefits, isValidEmployerBenefit) &&
    claim.employer_benefits.filter(isReportableBenefit).length > 0
  );
}

function getEmployerBenefitIntroPrompt(claim: ValidClaim) {
  const [, leavePeriodEnd] = getLeavePeriod(claim.leave_details);
  const [, waitingPeriodEnd] = getWaitingPeriod(claim.leave_details);
  const dayAfterWaitingPeriod = addDays(waitingPeriodEnd, 1);
  const basePrompt =
    "We need to know if you will receive any employer benefit payments that will cover your wages in full on any day after your PFML waiting period ends.";

  if (
    parseISO(leavePeriodEnd) < dayAfterWaitingPeriod ||
    !claim.previous_leaves
  ) {
    return basePrompt;
  }

  const waitingPeriodEndText = formatDateAsText(waitingPeriodEnd);
  return `${basePrompt} Your waiting period ends ${waitingPeriodEndText}.`;
}

function getEmployerBenefitFormPrompt(claim: ValidClaim) {
  const [, leavePeriodEnd] = getLeavePeriod(claim.leave_details);
  const [, waitingPeriodEnd] = getWaitingPeriod(claim.leave_details);
  const dayAfterWaitingPeriod = addDays(waitingPeriodEnd, 1);
  const basePrompt = `Will you receive benefit payments from your employer EIN ${claim.employer_fein} that will cover your wages in full`;

  if (parseISO(leavePeriodEnd) <= dayAfterWaitingPeriod) {
    return `${basePrompt} while on PFML?`;
  }

  const preposition = getDatePromptPreposition(claim);
  const dayAfterWaitingPeriodText = formatDateAsText(dayAfterWaitingPeriod);
  const leavePeriodEndText = formatDateAsText(parseISO(leavePeriodEnd));
  const dateRange = `${dayAfterWaitingPeriodText} and ${leavePeriodEndText}`;
  return `${basePrompt} ${preposition} ${dateRange}?`;
}

function getDatePromptPreposition(claim: ValidClaim) {
  return claim.has_continuous_leave_periods ? "between" : "on any days between";
}

/**
 * Fills date fieldset component within the portal.
 * @param caption Fieldset caption
 * @param date in the format of yyyy-mm-dd
 * @example
 * fillDateFieldset("What was the first day of this leave?", "2021-01-17")
 */
export function fillDateFieldset(caption: string | RegExp, date: string): void {
  const [year, month, day] = date.split("-");
  inFieldsetLabelled(caption, () => {
    cy.contains("Month").type(`{selectall}{backspace}${month}`);
    cy.contains("Day").type(`{selectall}{backspace}${day}`);
    cy.contains("Year").type(`{selectall}{backspace}${year}`);
  });
}

export function uploadAdditionalCertificationDocument(docName: string): void {
  cy.contains("a", /Upload (additional )?documents/).click();
  cy.contains("label", "Certification").click();
  cy.contains("button", "Save and continue").click();
  cy.contains("label", "I am uploading one complete document").click();
  cy.contains("button", "Save and continue").click();
  addLeaveDocs(docName);
  // Hotfix: Wait for this to complete, plus a margin.
  cy.wait("@documentUpload", { timeout: 30_000 })
    .its("response.statusCode")
    .should("eq", 200);
  cy.contains("You've successfully submitted your certification form", {
    timeout: 30_000,
  });
}

/**
 * @note Following section is related to claim amendments & review by Leave Admins
 * All of the functions assume you are navigated to the claim review page.
 */

const leaveAdminLeaveResponMap: Record<
  ValidPreviousLeave["leave_reason"],
  RegExp
> = {
  "An illness or injury":
    /An illness or injury|Leave to manage their serious health condition/,
  "Bonding with my child after birth or placement":
    /Bonding with their child after birth or placement/,
  "Caring for a family member who serves in the armed forces":
    /Caring for a family member who served in the armed forces/,
  "Caring for a family member with a serious health condition":
    /Caring for a family member/,
  "Managing family affairs while a family member is on active duty in the armed forces":
    /Managing family affairs while a family member was on active duty in the armed forces/,
  Pregnancy: /Pregnancy|Pregnancy or recovery from childbirth/,
  Unknown: /^$/,
};

export function amendWorkingHours(amendedHours: number): void {
  cy.findByText("Weekly hours worked")
    .parent()
    .parent()
    .findByText("Amend")
    .click();
  cy.findByLabelText(
    "On average, how many hours does the employee work each week?"
  ).type(`{selectAll}{backspace}${amendedHours}`);
}

/**
 * In the LA review screen we find the needed leave by the combination of it's dates & leave reason
 */
type LeaveIdentifier = Pick<
  ValidPreviousLeave,
  "leave_start_date" | "leave_end_date" | "leave_reason"
>;
/**
 * Finds a past leave and amends it with given information.
 * @param identifier Leave dates and leave reason in an object.
 * @param amendments Full data of the amended leave, think of PUT instead of PATCH.
 */
export function amendPreviousLeave(
  identifier: LeaveIdentifier,
  amendedLeave: ValidPreviousLeave
): void {
  // Setup the regex template
  // There's no unqiue identifier for listed leaves, so we have to use a combination of dates and reason.
  const template = `${dateToReviewFormat(
    identifier.leave_start_date
  )}.*${dateToReviewFormat(identifier.leave_end_date)}.*${
    leaveAdminLeaveResponMap[identifier.leave_reason].source
  }`;
  const selector = new RegExp(template);
  cy.contains("tr", selector).findByText("Amend").click();
  // The next table row will now contain the amendment form.
  cy.contains("tr", selector)
    .next()
    .within(() => {
      fillPreviousLeaveData(amendedLeave);
      cy.contains("button:not([disabled])", "Confirm amendment").click();
    });
}

interface AddPreviousLeaveOptions {
  readonly expectedError?: string;
}

export function addPreviousLeave(
  leave: ValidPreviousLeave,
  options?: AddPreviousLeaveOptions
): void {
  cy.findByText(/Add a(nother)? previous leave/).click();

  cy.contains("tr", "Add a previous leave").within(() => {
    fillPreviousLeaveData(leave);
    cy.contains("button:not([disabled])", "Confirm addition").click();
    if (options?.expectedError) {
      cy.contains(".usa-alert", options?.expectedError);
    }
  });
}

export function cancelAddingPreviousLeave(): void {
  cy.contains("tr", "Add a previous leave").within(() => {
    cy.findByText("Cancel addition").click();
  });
}
export function assertPreviousLeave(leave: ValidPreviousLeave): void {
  const template = `${dateToReviewFormat(
    leave.leave_start_date
  )}.*${dateToReviewFormat(leave.leave_end_date)}.*${
    leaveAdminLeaveResponMap[leave.leave_reason].source
  }`;
  const selector = new RegExp(template);

  cy.contains("table", "Leave type").within(() => {
    cy.contains("tr", selector).should(($tr) => {
      expect($tr.html()).to.match(selector);
    });
  });
}

function fillPreviousLeaveData(leave: ValidPreviousLeave): void {
  // The confirmation button will be disabeled unless a leave reason is entered.
  inFieldsetLabelled("Why did this employee need to take leave?", () =>
    cy.findByText(leaveAdminLeaveResponMap[leave.leave_reason]).click()
  );
  // Fill start date
  fillDateFieldset(
    "When did the employee's leave start?",
    leave.leave_start_date
  );
  // Fill end date
  fillDateFieldset("When did the employee's leave end?", leave.leave_end_date);
}

export function assertEmployerBenefit(benefit: ValidEmployerBenefit): void {
  const template = `${dateToReviewFormat(
    benefit.benefit_start_date
  )}.*${dateToReviewFormat(benefit.benefit_end_date)}.*${benefit.benefit_type}`;
  const selector = new RegExp(template);
  cy.contains("table", "Benefit type").within(($table) => {
    expect($table.html()).to.match(selector);
  });
}

export function addInsurancePlan(benefit: ValidEmployerBenefit): void {
  cy.findByText("Add a benefit").click();
  cy.contains(
    "tr",
    "Add an other disability, family, or medical leave plan"
  ).within(() => {
    fillInsurancePlanData(benefit);
    cy.contains("button:not([disabled])", "Confirm addition").click();
  });
}

function fillInsurancePlanData(benefit: ValidEmployerBenefit): void {
  inFieldsetLabelled("What kind of benefit is it?", () =>
    cy.findByText(getBenefitTypeLabel(benefit.benefit_type)).click()
  );
  fillDateFieldset(
    "What is the first day that this benefit will fully cover the employee’s wages?",
    benefit.benefit_start_date
  );
  fillDateFieldset(
    "What is the last day that this benefit will fully cover the employee’s wages?",
    benefit.benefit_end_date
  );
}

export function assertReportingImpactsEmployeeAlert() {
  cy.contains(
    ".usa-alert",
    "Information you report may impact your employee’s benefits."
  );
}

export function assertEmployeeReportedOtherLeaveAndBenefitsAlert() {
  cy.contains(
    ".usa-alert",
    "The information your employee reported may impact their benefits."
  );
}

export function assertReportingPreviousLeaveAlert() {
  cy.contains(
    ".usa-alert",
    "Reporting previous leave may reduce the amount of time your employee has available."
  );
}

export function assertReportingPTOAlert() {
  cy.contains(
    ".usa-alert",
    "Reporting PTO may impact your employee’s benefits."
  );
}

export function assertReportingInsurancePlanAlert() {
  cy.contains(
    ".usa-alert",
    "Reporting insurance policies may impact your employee’s benefits."
  );
}

export type LeaveType =
  | "Active duty"
  | "Military family"
  | "Bond with a child"
  | "Care for a family member"
  | "Medical leave"
  | "Medical leave for pregnancy or birth";
/**
 * Assert leave type of the claim during the review.
 * @param leaveType expand the type as needed
 */
export function assertLeaveType(leaveType: LeaveType): void {
  cy.findByText(leaveType, { selector: "h3" });
}

export type ClaimantStatusFilters =
  | "Approved"
  | "Denied"
  | "Withdrawn"
  | "Pending"
  | "Cancelled";

export type ReviewStatusOptions =
  | "Yes, review requested"
  | "No, review not needed";

export type OrgFilter = Record<"organization", string | number>;

export interface PaymentDetail {
  leavePeriod: [Date, Date];
  grossPayment: number;
  medicalLeaveInsurance: number;
  otherEmploymentIncome: number;
  childSupportReduction: number;
  sit: number;
  fit: number;
  netPayment: number;
}

/**Filter claims by given parameters
 * @param reviewOption Choice of review option [string].
 * @param claimStatusFilters Array of claim status options
 */
export function filterLADashboardBy(
  reviewOption: ReviewStatusOptions,
  claimStatusFilters: ClaimantStatusFilters[]
): void {
  for (const status_filter of claimStatusFilters) {
    cy.findByLabelText(status_filter).clickAction();
    clickApplyFilters();
    checkDashboardIsEmpty().then((hasNoClaims) => {
      if (hasNoClaims) {
        clickShowFilterButton();
        return;
      }
      assertClaimsHaveStatus(status_filter);
      if (reviewOption == "Yes, review requested") {
        assertClaimsNeedsReview("Review Application");
      }
      clickShowFilterButton();
      return;
    });
  }
  clearFilters();
}

export function selectOrgFilter(filter: OrgFilter): void {
  const { organization } = filter;
  const options = `select[name="employer_id"] > option`;
  const dropdown = `select[name="employer_id"]`;
  if (typeof organization === "string") {
    cy.get(options).select(organization);
  } else {
    cy.get(options)
      .eq(organization)
      .then((element) =>
        cy
          .get(dropdown)
          .invoke("css", "position", "unset")
          .select(element.val() as string)
      );
  }
}

/**Looks if dashboard is empty */
function checkDashboardIsEmpty(): Cypress.Chainable<boolean> {
  return cy
    .contains("table", "Application ID")
    .find("tbody tr")
    .then(($tr) => {
      return $tr.text() === "No applications on file";
    });
}

/**Clicks Show Filter Button */
export function clickShowFilterButton(): void {
  cy.get('button[aria-controls="filters"]')
    .invoke("text")
    .then((text) => {
      if (text.includes("Show filters")) {
        cy.findByText("Show filters", { exact: false }).click();
      }
    });
  cy.findByText("Hide filters").should("be.visible");
}

/**Clicks Apply Filter Button */
export function clickApplyFilters(): void {
  cy.findByText("Apply filters").should("not.be.disabled").click();
  cy.get('span[role="progressbar"]').should("be.visible");
  cy.wait("@dashboardClaimQueries");
  cy.contains("table", "Application ID").should("be.visible");
}

/**Asserts that all claims visible on the page need review */
function assertClaimsNeedsReview(status: string): void {
  cy.contains("table", "Application ID")
    .find('tbody tr td[data-label="Review due date"] a')
    .each((el) => {
      expect(el).to.contain.text(status);
    });
}

/**Asserts that all claims visible on the page have a status */
export function assertClaimsHaveStatus(status: ClaimantStatusFilters): void {
  cy.contains("table", "Application ID")
    .find('tbody tr td[data-label="Leave details"]')
    .each((el) => {
      expect(el).to.contain.text(status);
    });
}

export function clearFilters(): void {
  cy.get('button[aria-controls="filters"]')
    .invoke("text")
    .then((text) => {
      if (text.includes("Show filters")) {
        cy.findByText("Show filters", { exact: false }).click();
      }
      // Using the standard `.click` method just causes the filter menu to close.
      // Using `.trigger("click")` ensures that Cypress interacts with the element correctly.
      cy.contains("button", "Reset all filters").trigger("click");
      cy.wait("@dashboardClaimQueries");
      cy.contains("table", "Application ID").should("be.visible");
    });
}

/**Sorts claims on the dashboard*/
export function sortClaims(
  by: "new" | "old" | "name_asc" | "name_desc" | "status",
  assertQuery = true
): void {
  const sortValuesMap = {
    new: {
      // Value of the <option> tag for the sort select
      value: "latest_follow_up_date,ascending",
      // Query associated with it
      query: "order_by=latest_follow_up_date&order_direction=descending",
    },
    old: {
      value: "latest_follow_up_date,ascending",
      query: "latest_follow_up_date&order_direction=ascending",
    },
    name_asc: {
      value: "employee,ascending",
      query: "order_by=employee&order_direction=ascending",
    },
    name_desc: {
      value: "employee,descending",
      query: "order_by=employee&order_direction=descending",
    },
    status: {
      value: "absence_status,ascending",
      query: "fineos_absence_status&order_direction=ascending",
    },
  };
  cy.findByLabelText("Sort").then((el) => {
    if (el.val() === sortValuesMap[by].value) {
      return;
    }
    cy.wrap(el).select(sortValuesMap[by].value);
    cy.get('span[role="progressbar"]').should("be.visible");
    if (assertQuery) {
      cy.wait("@dashboardClaimQueries")
        .its("request.url")
        .should("include", sortValuesMap[by].query);
    }
  });
}

export function claimantGoToClaimStatus(
  fineosAbsenceId: string,
  options?: {
    waitForAliases?: Array<"@benefitYearsSearch">;
    visitLink?: boolean;
  }
): void {
  const waitForAliases = options?.waitForAliases ?? [];
  const visitLink = options?.visitLink ?? false;

  actionWithSkippedGetRequests(["documents", "modifications"], () => {
    if (waitForAliases.length > 0) {
      cy.wait(waitForAliases, {
        requestTimeout: 30_000,
      }).wait(250);
    }
    if (visitLink) {
      cy.visit(`/applications/status/?absence_id=${fineosAbsenceId}`);
    } else {
      cy.contains("article", fineosAbsenceId, { timeout: 15_000 }).within(
        () => {
          cy.contains("View status updates and details").click({
            timeout: 20_000,
          });
        }
      );
    }
    cy.url()
      .should("include", "/applications/status/")
      .and("include", fineosAbsenceId);
  });

  cy.wait(["@getModification", "@payments", "@getDocuments"]).wait(150);
}

const leaveReasonHeadings: Readonly<
  Partial<Record<NonNullable<LeaveReason>, string | RegExp>>
  // @Note: The below regex is NOT being used for
  // backwards compatibility but instead to switch
  // between status headings of claimant & portal pages
> = {
  "Serious Health Condition - Employee":
    /Medical leave|Leave to manage your own serious health condition/,
  "Child Bonding": /(Leave to b|B)ond with a child/,
  "Care for a Family Member": "Leave to care for a family member",
  "Military Exigency Family": "Active duty",
  "Pregnancy/Maternity":
    /Leave for pregnancy or recovery from childbirth|Medical leave for pregnancy or birth/,
} as const;

interface ApplicationTimelineStep {
  name: ApplicationTimelineStepName;
  status: ApplicationTimelineStepStatus;
}

type ApplicationTimelineStepName =
  | "Decision"
  | "DFML review"
  | "Documents submitted"
  | "Employer response"
  | "Request for More Information"
  | "Submit proof of adoption"
  | "Proof of birth submitted"
  | "Submit proof of birth"
  | "Submit proof of placement";

type ApplicationTimelineStepStatus =
  | "Action required"
  | "Completed"
  | "In progress"
  | "Next";

export function claimantAssertApplicationTimeline(
  steps: readonly ApplicationTimelineStep[]
): void {
  steps.forEach(assertApplicationTimelineStep);
}

function assertApplicationTimelineStep(step: ApplicationTimelineStep) {
  cy.contains(`${step.status}: ${step.name}`);
}

type LeaveStatus = {
  leave: keyof typeof leaveReasonHeadings;
  status: ClaimantStatusFilters;
  leavePeriods?: [string, string];
  leavePeriodType?: "Continuous" | "Intermittent" | "Reduced";
};

export function claimantAssertClaimStatus(leaves: LeaveStatus[]): void {
  for (const { leave, status, leavePeriods } of leaves) {
    const heading = leaveReasonHeadings[leave];
    if (!heading) {
      throw Error(
        `Leave reason "${leave}" property is undefined in Object "leaveReasonHeadings"`
      );
    }
    cy.contains(heading)
      .parent()
      .within(() => {
        cy.contains(leaveStatusPhrases[status]);

        if (leavePeriods) {
          assertClaimantLeavePeriodDates(leavePeriods[0], leavePeriods[1]);
        }
      });
  }
}

const leaveStatusPhrases = {
  Approved: "This leave was approved.",
  Cancelled: "This leave was cancelled.",
  Denied: "This leave was denied.",
  Pending: "Pending",
  Withdrawn: "This leave was withdrawn.",
} as const;

function assertClaimantLeavePeriodDates(
  start: number | string | Date,
  end: number | string | Date
) {
  const formattedStart = formatDateAsText(start);
  const formattedEnd = formatDateAsText(end);
  cy.contains("p", `From ${formattedStart} to ${formattedEnd}`);
}

/**
 * Asserts that the Languages menu is visible in the Portal header.
 *
 * The Languages menu was formerly known as the localize widget
 */
export function assertLanguagesMenuIsVisible() {
  // There is not a more semantic way to find this container other than using
  // this test ID. The document <header /> is a child of this container and does
  // not contain the Languages menu.
  cy.findByTestId("Header").within(() => {
    cy.contains("button", "Languages").should("be.visible");
  });
}

/**
 * Stubs the response of `GET: /applications` endpoint to contain no applications.
 * Claimant is redirected straight to "Start Application" page if he has no open aplications.
 */
export const skipLoadingClaimantApplications = (): void => {
  // Possible point of flaky behavior. Sometimes this intercept does not happen
  // before the timeout on whatever assertion or command follows it.
  cy.intercept(
    /\/api\/v1\/(applications\?|applications$)/,
    { times: 1, method: "GET" },
    (req) => {
      req.reply((res) => {
        res.body.data = [];
        res.send();
      });
    }
  ).wait(500);
};

/**
 * Search claims in LA dashboard by id or employee name.
 */
export function findClaim(idOrName: string): void {
  cy.findByLabelText("Search for employee name or application ID").type(
    `${idOrName}{enter}`
  );
  cy.get('span[role="progressbar"]').should("be.visible");
  cy.wait("@dashboardClaimQueries");
}

/**
 * Search claims in LA dashboard by id or employee name.
 * Expects to only find a single match.
 * @param idOrName - claim ID or name of the employee
 * @param expectSingleMatch - searching by name can yield more than 1 match, default `true`
 */
export function searchClaims(idOrName: string, expectSingleMatch = true): void {
  // Search
  findClaim(idOrName);
  // Assert match
  cy.get("table tbody tr")
    .should(expectSingleMatch ? "have.lengthOf" : "have.length.gte", 1)
    .each((el) => {
      expect(el).to.contain.text(idOrName);
    });
}

/**
 * Reset LA dashboard search input to empty state to show all of available claims.
 */
export function clearSearch(): void {
  cy.findByLabelText("Search for employee name or application ID").type(
    `{selectAll}{backspace}{enter}`
  );
  cy.get('span[role="progressbar"]').should("be.visible");
  cy.wait("@dashboardClaimQueries");
  cy.get("table tbody").should(($table) => {
    expect($table.children().length).to.be.gt(1);
  });
}

export function addWithholdingPreference(withholding: boolean) {
  cy.contains(
    "Do you want us to withhold state and federal taxes from this paid leave benefit?"
  );
  cy.get("label")
    .contains(withholding ? "Yes" : "No")
    .click();
  cy.get("button").contains("Submit tax withholding preference").click();
}

export function claimantGoToReportLeaveHours() {
  const actualsLinkText = "View and report your leave hours";
  cy.contains("a", actualsLinkText).click();
  cy.contains("button", "Submit hours").should("exist");
}

export function claimantReportLeaveHours(leaveDate: Date, hours: string) {
  fillDateFieldset(
    "When did you take this leave?",
    formatDateAsIsoExt(leaveDate)
  );

  cy.findByLabelText("Hours").clear();
  cy.findByLabelText("Hours").type(hours);

  cy.contains("button", "Submit hours").click();
  cy.get(".usa-alert__heading", { timeout: 30_000 }).should(
    "have.text",
    "Your leave hours were submitted"
  );
}

export function claimantReportRedundantLeaveHours(date: string, hours: string) {
  fillDateFieldset("When did you take this leave?", formatDateAsIsoExt(date));
  cy.findByLabelText("Hours").clear();
  cy.findByLabelText("Hours").type(hours);

  cy.contains("button", "Submit hours").click();
  cy.get(".usa-error-message.measure-5", { timeout: 30_000 }).should(
    "have.text",
    `You have already reported hours for ${date}. Call (857) 972-9256 to add additional time or correct your reported hours.`
  );
}

export function verifyApplicationNav() {
  cy.contains("a", "Application").should("be.visible").click();
  cy.url().should("include", "/applications/status/");
}

export function verifyPaymentStatus() {
  cy.contains("a", "Payments").should("be.visible").click();
  cy.url().should("include", "/applications/status/payments/");
  cy.contains("h2", "Your payments").should("be.visible");
}

export function viewPaymentStatus() {
  cy.contains("a", "Payments").click();
  cy.contains("Your payments");
}

export type PaymentStatus = {
  amount: string;
  payPeriod: string;
  status: string | RegExp;
  waitingPeriod: string;
};

export function assertPayments(payments: PaymentStatus[]) {
  cy.get("section[data-testid='your-payments']").within(() => {
    payments.forEach((payment) => {
      cy.contains("td[data-label='Amount']", payment.amount);
      cy.contains("td", payment.status);
      if (payment.amount === "0.00") {
        cy.contains("td[data-label='Waiting period']", payment.waitingPeriod);
      } else {
        cy.contains("td[data-label='Pay period']", payment.payPeriod);
      }
    });
  });
}

export function clickButton(btnValue: string): void {
  cy.contains("button", btnValue).click();
}

export function waitForPageLoad() {
  cy.wait(50);
  cy.get("main span[aria-label^='Loading']", { timeout: 20_000 }).should(
    "not.exist"
  );
}

export function assertTrustedDeviceList(isTrusted: boolean): void {
  goToSettingsPage();
  if (isTrusted) {
    cy.contains("h2", "Trusted devices").should("be.visible");
    // This selector matches 4 different elements on the page.
    // The one we want to target is the last match, which is the trusted devices section.
    cy.get('div[data-testid="ReviewRow"]')
      .last()
      .within(() => {
        cy.contains("div", "Last login:").should("be.visible");
        cy.contains("button", "Remove").should("be.visible");
      });
  } else {
    cy.contains("h2", "Trusted devices").should("be.visible");
    cy.contains("div", "You do not have any trusted devices.").should(
      "be.visible"
    );
  }
  cy.contains("a", "Back").click({ waitForAnimations: true });
  cy.wait(1000);
}

export function leaveAdminAssertClaimStatus(leaves: LeaveStatus[]) {
  for (const l of leaves) {
    const { leavePeriods, leave, status } = l;

    if (leavePeriods) {
      const formatStart = format(new Date(leavePeriods[0]), "M/d/yyyy");
      const formatEnd = format(new Date(leavePeriods[1]), "M/d/yyyy");
      cy.get(`th[data-label="Leave duration"]`).should(
        "contain.text",
        `${formatStart} to ${formatEnd}`
      );
    }
    const heading = leaveReasonHeadings[leave];
    if (!heading) {
      throw Error(
        `Leave reason "${leave}" property is undefined in Object "leaveReasonHeadings"`
      );
    }
    cy.contains(heading);
    cy.get('[data-label="Status"]').should("contain.text", status);
  }
}

/**
 * The date displayed on the portal is calculated in the portal and intentionally doesn't take into account holidays.
 * See EDM-1264 for more details.
 */
export function assertPaymentCheckBackDate(date: Date) {
  const dateString = format(date, "MMMM d, yyyy");
  cy.get("section[data-testid='your-payments-intro']").within(() => {
    cy.contains(`The first payment will be processed on ${dateString}`);
  });
}

export function resumeFineosApplication(ssn: string, absenceCaseId: string) {
  cy.location("pathname", { timeout: 30_000 }).should(
    "include",
    "/applications/get-ready/"
  );
  cy.contains("Did you start an application by phone?").click();
  cy.get("a[href$='/applications/import-claim/']").click();
  cy.get("input[name='tax_identifier']").clear();
  cy.get("input[name='tax_identifier']").type(ssn);
  cy.get("input[name='absence_case_id']").clear();
  cy.get("input[name='absence_case_id']").type(absenceCaseId);
  cy.contains("button[type='submit']", "Add application").click();
}

export function assertClaimImportError(errorMessage: string) {
  cy.contains("h2", "An error occurred")
    .next()
    .should("have.text", errorMessage);
}

export function assertClaimImportSuccess(absenceCaseId: string) {
  cy.contains(
    "div.usa-alert",
    `Application ${absenceCaseId} has been added to your account.`
  );
  cy.wait(200);
  cy.contains("a", "Continue application").click({ timeout: 30_000 });
  cy.contains("h1", "Your in-progress application");
}

/**
 * Asserts for claim statuses within the leave admin dashboard
 * @param leaves - LeaveStatus[]
 * @example leaveAdminAssertClaimStatusFromDashboard([{}])
 */
export function leaveAdminAssertClaimStatusFromDashboard(
  leaves: LeaveStatus[]
) {
  cy.get("table tbody").within(() => {
    for (const { leave, status, leavePeriods, leavePeriodType } of leaves) {
      if (leavePeriods) {
        const formatStart = format(new Date(leavePeriods[0]), "M/d/yyyy");
        const formatEnd = format(new Date(leavePeriods[1]), "M/d/yyyy");
        cy.contains(
          "td[data-label='Leave details']",
          `${formatStart} to ${formatEnd}`
        );
      }
      if (leavePeriodType) {
        cy.contains("td[data-label='Leave details']", leavePeriodType);
      }
      cy.contains("td[data-label='Leave details']", status);
      const leaveReason = leaveReasonHeadings[leave];
      if (!leaveReason) {
        throw Error(
          `Leave reason "${leave}" property is undefined in Object "leaveReasonHeadings"`
        );
      }
      cy.contains("td[data-label='Leave details']", leaveReason);
    }
  });
}
type LeaveAdminContactInformation = {
  firstName: string;
  lastName: string;
  phoneNumber: string;
};
export function enterLeaveAdminContactInformation(
  contactInformation?: LeaveAdminContactInformation,
  isEdit = false
) {
  if (isEdit) {
    cy.contains("a", "Edit").click();
  }
  cy.contains(/How should we contact you/);
  if (contactInformation) {
    /**
     * CYPRESS SUGGESTED TO BREAKING UP A CHAIN

     * Cypress initially found matching element(s), but while waiting for them to become actionable,
       they disappeared from the page. Common situations why this happens:
        - Your JS framework re-rendered asynchronously
        - Your app code reacted to an event firing and removed the element

        You can typically solve this by breaking up a chain. For example, rewrite:

        > cy.get('button').click().click()

        to

        > cy.get('button').as('btn').click()
        > cy.get('@btn').click()


      So below we had to detach the chains and update it to this structure to make our specs more stable.
     */

    cy.findByLabelText("First name")
      .as("firstName")
      .should("be.visible", { timeout: 6000 })
      .should("be.enabled", { timeout: 6000 })
      .wait(300);
    cy.get("@firstName").type(
      `{selectAll}{backspace}${contactInformation.firstName}`
    );
    cy.findByLabelText("Last name")
      .as("lastName")
      .should("be.visible", { timeout: 6000 })
      .should("be.enabled", { timeout: 6000 })
      .wait(300);
    cy.get("@lastName").type(
      `{selectAll}{backspace}${contactInformation.lastName}`
    );
    cy.findByLabelText("Phone number")
      .as("phoneNumber")
      .should("be.visible", { timeout: 6000 })
      .should("be.enabled", { timeout: 6000 })
      .wait(300);
    cy.get("@phoneNumber").type(
      `{selectAll}{backspace}${contactInformation.phoneNumber}`
    );
  }
  cy.get("button[type='submit']").click();

  if (!contactInformation) {
    cy.contains("First name is required");
    cy.contains("Last name is required");
    cy.contains("Phone number is required");
  }
}

export function getLeaveAdminName() {
  return cy
    .get("aside .text-bold > var")
    .invoke("text")
    .then((text) => {
      return text;
    });
}

export function assertLeaveAdminContactInformation({
  firstName,
  lastName,
  phoneNumber,
}: LeaveAdminContactInformation) {
  cy.get("aside").within(() => {
    cy.contains(firstName);
    cy.contains(lastName);
    const formattedPhoneNumber = phoneNumber
      .split("-")
      .map((substr, i) => {
        // Anonymize the first 7 digits to match what is diplayed in the portal.
        if (i == 0 || i == 1) {
          return substr.replace(/[0-9]/g, "*");
        } else {
          return substr;
        }
      })
      .join("-");
    cy.contains(formattedPhoneNumber);
  });
}

export function requestModification(
  type: "extension" | "endLeaveEarly",
  newEndDate: Date,
  options: RequestModificationOptions = {}
) {
  const { crossesBenefitYear, currentBenefitYearDates, uploadDoc } = options;
  const typeOfModification =
    type === "extension" ? "Extend my leave" : "End my leave early";

  cy.contains("Start a change request").should("be.visible").click();
  cy.get('button[type="submit"]').should("be.visible").click();

  cy.contains("label", typeOfModification).should("be.visible").click();
  cy.findByLabelText("Month").type((getMonth(newEndDate) + 1).toString());
  cy.findByLabelText("Day").type(format(newEndDate, "d"));
  cy.findByLabelText("Year").type(getYear(newEndDate).toString());

  cy.get("button[type='submit']").click();
  cy.wait("@changeRequest").wait(150);

  if (crossesBenefitYear) {
    cy.contains(
      "The leave dates you’re requesting cross into a new benefit year."
    );

    if (currentBenefitYearDates) {
      const { end, start } = currentBenefitYearDates;
      const currentFormattedStart = formatIsoDateStringAsText(start);
      const currentFormattedEnd = formatIsoDateStringAsText(end);
      cy.contains(
        `Your existing benefit year is ${currentFormattedStart} to ${currentFormattedEnd}.`
      );

      const [nextStart, nextEnd] = calculateNextBenefitYear(end);
      const nextFormattedStart = formatIsoDateStringAsText(nextStart);
      const nextFormattedEnd = formatIsoDateStringAsText(nextEnd);
      cy.contains(
        `Your new benefit year is ${nextFormattedStart} to ${nextFormattedEnd}.`
      );
    }

    cy.findByText("I understand").click();
  }

  if (uploadDoc) {
    addUpdatedLeaveDocs("HCP");
  }

  cy.contains(
    /(Review and submit your requested change|Are you sure you want to cancel your entire PFML leave)/
  );
  cy.get("button[type='submit']").click();
  cy.contains(/(You|We)(’|')ve (submitted|received) your change request/);
}

export interface RequestModificationOptions {
  crossesBenefitYear?: boolean;
  currentBenefitYearDates?: {
    end: string;
    start: string;
  };
  uploadDoc?: boolean;
}

// Add leave docs for a modification.
function addUpdatedLeaveDocs(leaveType: string) {
  cy.findByLabelText(/Choose page 1/).selectFile(
    getDocPath(`${leaveType}.pdf`),
    { force: true }
  );
  cy.contains("button", "Save and continue").click();
}

type AppealFormData = {
  reason?: string;
  phoneNumber?: string;
};
export function appeal(
  uploadAdditionalDocs: boolean,
  appealInformation?: AppealFormData
) {
  cy.contains("a", "Start an appeal").click();
  cy.contains("button", "Continue").click();
  cy.contains("What is the reason for the appeal?");
  if (appealInformation?.reason) {
    cy.get("textarea[name='appeal_reason']").type(appealInformation?.reason);
  }
  cy.contains("button", "Save and continue").click();
  cy.contains("What is the best phone number to reach you?");
  cy.get("input[name='appeal_phone_number.phone_number']").type(
    appealInformation?.phoneNumber ?? "************"
  );
  cy.contains("button", "Save and continue").click();
  cy.contains("Will you need an interpreter?");
  cy.findByLabelText("No").clickAction();
  cy.contains("button", "Save and continue").click();
  cy.contains("Will you have a representative?");
  cy.findByLabelText("No").clickAction();
  cy.contains("button", "Save and continue").click();
  cy.contains("When did you receive the DFML decision that you’re appealing?");
  enterDate(new Date());
  cy.contains("button", "Save and continue").click();
  cy.contains("button", "Submit appeal").click();
  cy.contains("Your appeal has been received", { timeout: 30_000 });
  if (uploadAdditionalDocs) {
    cy.contains("Have documents to upload?");
    cy.findByLabelText("Yes").clickAction();
    cy.contains("button", "Continue").click();
    addId();
    // @todo: "You've successfully submitted your appeals documents" appeals is the latest version
    cy.contains(
      /You've successfully submitted your (supporting|appeals) documents/
    );
  }
  return;
}

export function viewPaymentDetails(period: CompletedPaymentPeriod) {
  cy.contains(
    `See details for ${format(period.datePeriodStart, "M/d/yyyy")} to ${format(
      period.datePeriodEnd,
      "M/d/yyyy"
    )}`
  ).click();
}

export function assertPaymentDetails(paymentDetails: PaymentDetail[]) {
  // keys represent payment categories, while values represent unique identifying text for each payment row
  const paymentCategoryTableMap: Record<
    keyof Omit<PaymentDetail, "leavePeriod">,
    string
  > = {
    grossPayment: "Gross payment amount",
    netPayment: "Net payment amount",
    sit: "state income tax",
    fit: "federal income tax",
    otherEmploymentIncome:
      "Reduction due to earnings from another employment or self-employment you reported",
    medicalLeaveInsurance:
      "Reduction due to family or medical leave insurance you or your employer reported",
    childSupportReduction: "Withholding for child support",
  };
  let paymentDetail: PaymentDetail;
  for (paymentDetail of paymentDetails) {
    const [start, end] = paymentDetail.leavePeriod;
    const paymentRows = (
      Object.keys(paymentDetail) as Array<keyof typeof paymentDetail>
    ).filter((key) => key !== "leavePeriod") as Array<
      keyof Omit<PaymentDetail, "leavePeriod">
    >;
    // find target table using payment period dates a look within table for assertions

    const assertDetails = () => {
      for (const paymentCategory of paymentRows) {
        cy.contains("tr", paymentCategoryTableMap[paymentCategory]).within(
          () => {
            const paymentAmount = paymentDetail[paymentCategory];
            if (paymentAmount) {
              cy.get("td[data-label=Amount]").should(
                "contain.text",
                numToPaymentFormat(paymentAmount)
              );
            }
          }
        );
      }
    };

    if (paymentDetails.length === 1) {
      // If there's only one payment, the header referenced in the `else` block doesn't exist.
      assertDetails();
    } else {
      cy.contains(`${format(start, "M/d/yyyy")} to ${format(end, "M/d/yyyy")}`)
        .parent()
        .within(() => {
          assertDetails();
        });
    }
  }
  cy.contains("Back to your payments").click();
}

export function goToOrganizationDetails(fein: string): void {
  cy.get("td")
    .contains(fein)
    .parent()
    .get('th[data-label="Organization"] a')
    .click();
  cy.wait("@getLeaveAdmins");
  cy.wait("@getLeaveAdmins");
}

export function stashExistingLeaveAdminEmail(leaveAdminUsername: string): void {
  cy.get("table.width-full tbody tr").then(function ($rows) {
    // Get all verified leave admins in the table sorted by their verified_at date
    const leave_admins: Record<string, string[]> = {};
    // For each row in the table, aka leave admin
    $rows.each((_, tr) => {
      // Make sure this is a verified leave admin row
      const $date = tr.querySelector('td[data-label="Verification method"]');
      if ($date == null) {
        return;
      }
      const verifiedText = "Verified MassTaxConnect data on ";
      if (!$date.textContent?.includes(verifiedText)) {
        return;
      }
      // Get the verified_at date
      const date = $date.textContent.replace(verifiedText, "");
      // Get their email address
      const email = tr.querySelector('td[data-label="Email"]')?.textContent;
      if (!email || email === leaveAdminUsername) {
        return;
      }
      // Sort the emails into respective verified_at date
      if (!(date in leave_admins)) {
        leave_admins[date] = [];
      }
      leave_admins[date].push(email);
    });
    // Sort the verified_at dates, aka the keys of the leave_admins object
    // to obtain the most recently added leave admin emails
    const mostRecentVerifiedDate = Object.keys(leave_admins).sort((d1, d2) =>
      new Date(d1) < new Date(d2) ? 1 : -1
    )[0];
    // Pick the first email for the given date (could have multiple)
    const existingRecentlyAddedLeaveAdminEmail =
      leave_admins[mostRecentVerifiedDate][0];
    // Save it for future assertions
    cy.stash("existingLeaveAdminEmail", existingRecentlyAddedLeaveAdminEmail);
    cy.log(`leaveAdminUsername: ${leaveAdminUsername}`);
    cy.log(`mostRecentVerifiedDate: ${mostRecentVerifiedDate}`);
    cy.log(`existingLeaveAdminEmail: ${existingRecentlyAddedLeaveAdminEmail}`);
    cy.log(
      `otherExistingLeaveAdminEmails: ${JSON.stringify(
        leave_admins,
        undefined,
        2
      )}`
    );
  });
}

function findLeaveAdmin(email: string): Cypress.Chainable<JQuery<HTMLElement>> {
  return cy.get("table tbody").find(`tr:contains("${email}")`);
}

export function addLeaveAdmin(email: string): void {
  cy.findByText("Add a leave administrator").click();

  cy.findByLabelText("Email address").type(email);
  cy.findByText("Agree and add leave administrator").click();
  cy.wait("@addLeaveAdmin");
  cy.wait("@getLeaveAdmins");
  findLeaveAdmin(email).contains("PENDING");
}

export function removeLeaveAdmin(email: string): void {
  findLeaveAdmin(email).contains("Remove").click();
  cy.findByText("Yes, remove leave administrator").click();
  cy.findByText("Remove leave administrator").click();
  cy.wait("@removeLeaveAdmin");
  cy.wait("@getLeaveAdmins");
  cy.findByText("Leave administrator removed");
  cy.contains(`${email} no longer has access`);
}

export function goToLeaveAllotmentForClaimantByAbsenceId(
  fineosAbsenceId: string
): void {
  cy.visit("/employers/applications/");
  cy.get('input[name="search"]').type(fineosAbsenceId);
  cy.contains("button", "Search").click();

  cy.contains("table", "Employee").within((_$table) => {
    cy.contains(fineosAbsenceId)
      .parents("tr")
      .within(() => {
        cy.get('th[data-label="Employee"]')
          .contains(/view leave allotment/i)
          .click();
      });
  });
}

export function goToLeaveAllotmentForClaimant(
  employerId: string,
  employeeId: string
): void {
  cy.visit(
    `/employers/applications/leave-allotment/?employer_id=${employerId}&employee_id=${employeeId}`
  );
}

export function verifyContinuousLeaveAllotmentForClaimant(
  claims: ContinuousLeavePeriod[],
  benefitYearEndDate?: string
): void {
  type ApprovedLeaveDuration = {
    approvedTimeRemainingLabel: string;
    approvedTimeInHours: number;
  };

  const benefitYearEndDateParsed = benefitYearEndDate
    ? parseISO(benefitYearEndDate)
    : undefined;

  const periods: ApprovedLeaveDuration[] = claims.map((claim) => {
    const startDate = parseISO(claim.start);
    const endDate = parseISO(claim.end);
    const adjustedEndDate =
      benefitYearEndDateParsed && endDate > benefitYearEndDateParsed
        ? benefitYearEndDateParsed
        : endDate;

    adjustedEndDate.setDate(adjustedEndDate.getDate() + 1);

    const approvedTimeInHours =
      differenceInBusinessDays(adjustedEndDate, startDate) * 8;

    return {
      approvedTimeRemainingLabel:
        buildExpectedTimeRemainingOutput(approvedTimeInHours),
      approvedTimeInHours,
    };
  });

  const overallApprovedTimeInHours = periods.reduce(
    (acc, period) => acc + period.approvedTimeInHours,
    0
  );
  const overallApprovedTimeInHoursLabel = buildExpectedTimeRemainingOutput(
    overallApprovedTimeInHours
  );

  const availableRemainingTimeInHours =
    maxAvailableTimeInBusinessHours - overallApprovedTimeInHours;
  const availableRemainingTimeInHoursLabel = buildExpectedTimeRemainingOutput(
    availableRemainingTimeInHours
  );

  const availableRemainingTimeInHoursMedicalLeave =
    maxAvailableMedicalLeaveTimeInBusinessHours - overallApprovedTimeInHours;
  const availableRemainingTimeInHoursMedicalLeaveLabel =
    buildExpectedTimeRemainingOutput(availableRemainingTimeInHoursMedicalLeave);

  validateLeaveAllotmentPage(
    overallApprovedTimeInHoursLabel,
    availableRemainingTimeInHoursLabel,
    availableRemainingTimeInHoursMedicalLeaveLabel,
    claims[0].leave_type!
  );
}

export type IntermittentLeavePeriodSpec = IntermittentLeavePeriods & {
  leave_type?: string;
};

export function verifyIntermittentLeaveAllotmentForClaimant(
  intermittentClaim: IntermittentLeavePeriodSpec
) {
  const { frequency_interval, duration } = intermittentClaim;

  const hoursInBusinessDay = 8;

  const totalApprovedHours =
    frequency_interval! * duration! * hoursInBusinessDay;
  const totalRemainingHours =
    maxAvailableTimeInBusinessHours - totalApprovedHours;
  const totalFamilyRemainingHours =
    maxAvailableFamilyLeaveTimeInBusinessHours - totalApprovedHours;

  const totalApprovedHoursLabel =
    buildExpectedTimeRemainingOutput(totalApprovedHours);
  const totalRemainingHoursLabel =
    buildExpectedTimeRemainingOutput(totalRemainingHours);
  const totalFamilyRemainingHoursLabel = buildExpectedTimeRemainingOutput(
    totalFamilyRemainingHours
  );

  validateLeaveAllotmentPage(
    totalApprovedHoursLabel,
    totalRemainingHoursLabel,
    totalFamilyRemainingHoursLabel,
    intermittentClaim.leave_type!
  );
}

function validateLeaveAllotmentPage(
  totalApprovedHoursLabel: string,
  totalRemainingHoursLabel: string,
  totalLeaveTypeRemainingHoursLabel: string,
  leaveType: string
) {
  cy.contains("Overall time in the benefit year")
    .parent()
    .within(() => {
      cy.contains(new RegExp(`^${totalApprovedHoursLabel}$`, "g")).should(
        "exist"
      );
      cy.contains(new RegExp(`^${totalRemainingHoursLabel}$`, "g")).should(
        "exist"
      );
    });

  cy.contains("Time approved and available by leave type")
    .parent()
    .within(() => {
      cy.get("table > tbody").within(() => {
        cy.log("leaveType: ", leaveType);
        cy.contains("tr", leaveType, { matchCase: false }).within(() => {
          cy.contains(new RegExp(`^${totalApprovedHoursLabel}$`, "g"));
          cy.contains(
            new RegExp(`^${totalLeaveTypeRemainingHoursLabel}$`, "g")
          );
        });
      });
    });
}

// This assumes that the claimants are working 5x8 hour weeks. This is a simplification and the work patterns
// can be different for each claimant. If we have to test for different work patterns, we will have to update this function.

const maxAvailableTimeInBusinessHours = maxAvailableLeaveTimeInWeeks.total * 40;
const maxAvailableMedicalLeaveTimeInBusinessHours =
  maxAvailableLeaveTimeInWeeks.medical * 40;
const maxAvailableFamilyLeaveTimeInBusinessHours =
  maxAvailableLeaveTimeInWeeks.family * 40;

function buildExpectedTimeRemainingOutput(totalHours: number): string {
  const weeks = Math.floor(totalHours / (8 * 5));
  const days = Math.floor((totalHours % (8 * 5)) / 8);
  const hours = totalHours % 8;

  const weekStr = weeks > 0 ? `${weeks} week${weeks > 1 ? "s" : ""}` : "";
  const dayStr = days > 0 ? `, ${days} day${days > 1 ? "s" : ""}` : "";
  const hoursStr = hours > 0 ? `, ${hours} hour${hours > 1 ? "s" : ""}` : "";

  return `${weekStr}${dayStr}${hoursStr}`;
}

export function assertCannotRemoveLeaveAdmin() {
  cy.findByText("Additional login verification required");
  cy.contains("Remove").first().click();
  //Backward Compatability
  // Do you want to add a cell phone number to verify it's you when you log in? -> Provide a phone number we can use when we need to verify your login
  cy.contains(
    /(Do you want to add a cell phone number to verify it's you when you log in|Provide a phone number we can use when we need to verify your login)/
  );
}

export function assertCanWithdrawApplication(fineosAbsenceId: string) {
  goToDashboardFromSuccessPage();
  waitForPageLoad();
  cy.get(`[href*='${fineosAbsenceId}']`);
  cy.contains("article", fineosAbsenceId).within(() => {
    cy.contains("Withdraw your application");
    cy.contains("Continue application").click();
  });
}

export function addBondingLeave(birthDate: Date) {
  const endDate = addDays(birthDate, 7);
  cy.stash("bondingEndDate", format(endDate, "MM/dd/yyyy"));
  cy.get("[href^='/applications/modify/med-to-bonding/']")
    .contains(/Add leave to bond with a child|Add family leave/)
    .click();

  cy.contains("Continue").click();

  enterDate(birthDate);
  cy.contains("Save and continue").click();

  cy.url().should("contain", "bonding-leave-dates");
  enterDate(endDate);
  cy.contains("Save and continue").click();
  cy.url().should("not.contain", "bonding-leave-dates");
  cy.url().then((url) => {
    if (url.includes("proof-of-birth")) {
      cy.contains("label", "No").click();
      cy.contains("Save and continue").click();
    }
  });
  cy.contains("Submit your request").click();
  cy.contains(/You(.)ve submitted your request for leave to bond with a child/);
}

function enterDate(date: Date) {
  cy.findByLabelText("Month").type(`${date.getMonth() + 1}`);
  cy.findByLabelText("Day").type(`${date.getDate()}`);
  cy.findByLabelText("Year").type(`${date.getFullYear()}`);
}

export function assertLeave(leaveType: string, status: string) {
  const card = cy.contains(".font-heading-md", leaveType).parent();
  card.should("contain", status);
}

export function withdrawClaim(fineosAbsenceId?: string) {
  if (fineosAbsenceId) {
    claimantGoToClaimStatus(fineosAbsenceId);
  }

  cy.contains("Start a request").click();
  cy.contains("Yes, withdraw my application").click();
  cy.url().should("include", "&changeRequestWithdrawnSuccessfully=true");
  cy.contains("You've withdrawn your application for leave");
}

export function resumeBondingLeave() {
  cy.get("[href*='/upload/proof-of-birth/']").click();
  addLeaveDocs("caring");
  cy.contains("You've successfully submitted your proof of birth documents");
}

export function assertTaxDocumentsPageUnavailable() {
  cy.contains("a", "Tax Documents").should("not.exist");
}
export function goToTaxDocumentsPage() {
  cy.contains("a", "Tax Documents").click();
  cy.url().should("contain", "/tax-documents");
}

export function assertNoTaxDocumentsAvailable() {
  cy.contains("h2", /Your account doesn('|’)t have any tax documents/).should(
    "be.visible"
  );
}

export function getApprovedClaimWithApprovedLeaveRequest() {
  return `
    select json_build_object(
        'fein', emp.employer_fein,
        'fineosId', c.fineos_absence_id
    ) from claim c
        JOIN absence_period AS ap ON ap.claim_id = c.claim_id
        JOIN employee AS emy on emy.employee_id = c.employee_id
        JOIN employee_occupation AS eo on eo.employee_id = emy.employee_id
        JOIN employer AS emp on emp.employer_id = eo.employer_id 
        JOIN lk_leave_request_decision
                ON lk_leave_request_decision.leave_request_decision_id = ap.leave_request_decision_id
        JOIN application AS app ON app.claim_id = c.claim_id 
        WHERE lk_leave_request_decision.leave_request_decision_description IN ('Approved')
            AND ap.leave_request_decision_id = 3
        LIMIT 1;
    `;
}

export function getClaimCompletedAndApprovedTodayQuery(email: string): string {
  return `SELECT json_build_object(
        'fineos_absence_id', c.fineos_absence_id,
        'created_at', c.created_at,
        'employer_fein', e.employer_fein
      )
      FROM claim AS c
      INNER JOIN application AS a ON c.claim_id = a.claim_id
      INNER JOIN public.user AS u ON a.user_id = u.user_id
      INNER JOIN employer AS e ON c.employer_id = e.employer_id
      WHERE u.email_address = '${email}'
        AND a.completed_time::date = CURRENT_DATE
        AND c.approval_date = CURRENT_DATE
              ORDER BY a.completed_time DESC LIMIT 1;
      `;
}

export function getClaimWithCompletedPaymentsQuery(username: string): string {
  return `SELECT json_build_object(
        'fineos_absence_id', c.fineos_absence_id,
        'created_at', c.created_at,
        'absence_period_start_date', ap.absence_period_start_date
      )
      FROM claim AS c
      INNER JOIN absence_period AS ap ON ap.claim_id = c.claim_id
      INNER JOIN application AS a ON c.claim_id = a.claim_id
      INNER JOIN public.user AS u ON a.user_id = u.user_id
      INNER JOIN employee_occupation AS eo ON eo.employee_id = c.employee_id
      INNER JOIN payment AS p ON c.claim_id = p.claim_id
      INNER JOIN lk_absence_status AS lkas ON c.fineos_absence_status_id = lkas.absence_status_id
      WHERE u.email_address = '${username}'
        AND a.completed_time > (CURRENT_DATE - INTERVAL '28 days')::timestamp
        AND a.completed_time < (CURRENT_DATE - INTERVAL '7 days')::timestamp
        AND p.has_active_writeback_issue = FALSE
        AND lkas.absence_status_description = 'Closed'
        AND p.amount > 0
      ORDER BY a.completed_time DESC
      LIMIT 1`;
}

export function getClaimCompletedAndApprovedQuery(email: string): string {
  return `SELECT json_build_object(
        'fineos_absence_id', c.fineos_absence_id,
        'created_at', c.created_at,
        'employer_fein', e.employer_fein
      )
      FROM claim AS c
      INNER JOIN application AS a ON c.claim_id = a.claim_id
      INNER JOIN public.user AS u ON a.user_id = u.user_id
      INNER JOIN employer AS e ON c.employer_id = e.employer_id
      WHERE u.email_address = '${email}'
        AND c.approval_date IS NOT NULL
          ORDER BY a.completed_time DESC
          LIMIT 1`;
}

export function getClaimWithIntermittentLeaveHoursQuery(email: string): string {
  return `SELECT json_build_object(
        'fineos_absence_id', c.fineos_absence_id,
        'created_at', c.created_at,
        'employer_fein', e.employer_fein
      )
      FROM claim AS c
      INNER JOIN application AS a ON c.claim_id = a.claim_id
      INNER JOIN public.user AS u ON a.user_id = u.user_id
      INNER JOIN employer AS e ON c.employer_id = e.employer_id
        WHERE u.email_address = '${email}'
        AND c.approval_date IS NOT NULL
        AND a.has_intermittent_leave_periods = TRUE
      ORDER BY a.completed_time DESC
      LIMIT 1`;
}

export function getClaimAfterNightlyExtractQuery(username: string): string {
  return `SELECT json_build_object(
            'fineos_absence_id', c.fineos_absence_id,
            'created_at', c.created_at,
            'employer_fein', e.employer_fein
          )
          FROM claim AS c
          INNER JOIN application AS a ON c.claim_id = a.claim_id
          INNER JOIN application_payment_preference AS app ON a.payment_preference_id = app.payment_pref_id
          INNER JOIN lk_payment_method AS pm ON app.payment_method_id = pm.payment_method_id
          INNER JOIN public.user AS u ON a.user_id = u.user_id
          INNER JOIN payment AS p ON c.claim_id = p.claim_id
          INNER JOIN state_log AS sl ON p.payment_id = sl.payment_id
          INNER JOIN latest_state_log AS lsl ON sl.state_log_id = lsl.state_log_id
          INNER JOIN employer AS e ON c.employer_id = e.employer_id
          INNER JOIN employee_occupation AS eo ON eo.hours_worked_per_week = 40
          INNER JOIN lk_absence_status AS lkas ON c.fineos_absence_status_id = lkas.absence_status_id
          INNER JOIN lk_state AS st ON sl.end_state_id = st.state_id
          WHERE u.email_address = '${username}'
            AND a.completed_time > (CURRENT_DATE - INTERVAL '28 days')::timestamp
            AND a.completed_time < (CURRENT_DATE - INTERVAL '7 days')::timestamp
            AND p.has_active_writeback_issue = FALSE
            AND lkas.absence_status_description IN ('Approved','Closed','Completed')
            AND p.amount > 0
            AND st.state_description IN ('Payment complete', 'PUB Transaction sent - Check', 'PUB Transaction sent - EFT', 'Payment Complete with change notification')
            AND pm.payment_method_description = 'Check'
          ORDER BY a.completed_time DESC
          LIMIT 1`;
}

export function findSentEmailQuery(applicationId: string, trigger: string) {
  return `SELECT json_build_object('created_at', ntf.created_at, 'fineos_absence_id', ntf.fineos_absence_id)
    FROM notification AS ntf
    INNER JOIN claim AS c
    ON ntf.fineos_absence_id = c.fineos_absence_id
    WHERE c.claim_id = '${applicationId}'
    AND ntf.request_json::json->>'trigger' = '${trigger}'
    LIMIT 1;`;
}

export function wrapUpPartOne(
  applicationId: string,
  application: ApplicationRequestBody
) {
  cy.visit(`/applications/checklist/?claim_id=${applicationId}`);
  waitForPageLoad();
  submitClaimPartOne(application, { stepsOneTwoComplete: true });
}

export function validateAlertWithoutERNotification(
  is_employer_notified: boolean | null | undefined
): void {
  if (is_employer_notified === false) {
    cy.contains(
      "Your leave request may be delayed or denied if you do not notify your employer of your leave at least 30 days before the start date."
    );
  }
}

function answerGenderAndLanguage(
  application: ApplicationRequestBody,
  options?: SubmitClaimPartOneOptions
) {
  cy.get("[data-cy='gender-form']").within(() => {
    cy.findByLabelText(application.gender as string).check();
    cy.contains("button", "Save and continue").click();
  });
  cy.get("[data-cy='ethnicity-form']").within(() => {
    inFieldsetLabelled("What is your ethnicity?", () => {
      cy.get("input[type='radio']").clickAction(
        application.ethnicity as string
      );
    });
    cy.contains("button", "Save and continue").click();
  });
  cy.get("[data-cy='race-form']").within(() => {
    inFieldsetLabelled("What is your race?", () => {
      cy.get("input[type='radio']").clickAction(
        "Another race not listed above"
      );
    });
    cy.contains(
      "If you identify as more than one race, you are welcome to write them all out."
    ).should("be.visible");
    inFieldsetLabelled("What is your race?", () => {
      cy.get("input[type='radio']").clickAction(application.race as string);
    });
    cy.contains("button", "Save and continue").click();
  });
  cy.contains(
    "In what language would you like to receive future written communications?"
  );

  if (options?.attemptNotPickingLanguage) {
    cy.contains("button", "Save and continue").click();

    cy.contains(
      ".usa-alert--error",
      "Select a language for future written communications."
    );
  }

  cy.contains(
    "form",
    "In what language would you like to receive future written communications?"
  ).within(() => {
    const languageLabel = getLanguageLabel(application.language || "English");
    cy.findByLabelText(languageLabel).clickAction();
    cy.contains("button", "Save and continue").click();
  });
}

function maybeUpdateMMGProfile() {
  cy.url()
    .should("not.include", "/applications/review/")
    .then((url) => {
      if (url.includes("/applications/save-profile")) {
        const continueButton = hasProfileIdvFeature
          ? "Continue without updating"
          : "Save and continue";
        cy.contains(continueButton).click();
      } else {
        cy.log("No updates for MMG profile.");
      }
    });
}

function maybeAutofillMMGProfile() {
  cy.url()
    .should("not.include", "/applications/start/")
    .then((url) => {
      if (url.includes("/applications/use-profile/")) {
        cy.contains("button", "Not this time").click();
      } else {
        cy.log("No usable data in MMG profile.");
      }
    });
}
export function assertLeaveHoursDisplayInPortal(
  arg: string[],
  rowText: string
) {
  const argReadyForAssertion = arg.join("");
  cy.wrap(rowText).should("eq", argReadyForAssertion);
}
